/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as c}from"./request-BNOh435t.js";/* empty css                    */import{r as I,c as g,a as u,n as J,b as o,z as P,w as n,A as T,B as $,C as A,D as F,o as _,j as p,G as M,u as b,I as O,J as j,K as q,L as G,N as y,E as d}from"./index-DXyN40AS.js";const K={class:"card",style:{"margin-bottom":"5px"}},L={class:"card",style:{"margin-bottom":"5px"}},H={class:"card",style:{"margin-bottom":"5px"}},Q={key:0,class:"card"},R={class:"dialog-footer"},ce={__name:"Notice",setup(W){const t=I({formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,title:null,ids:[]}),r=()=>{c.get("/notice/selectPage",{params:{pageNum:t.pageNum,pageSize:t.pageSize,title:t.title}}).then(l=>{var e,i;l.code==="200"&&(t.tableData=((e=l.data)==null?void 0:e.list)||[],t.total=(i=l.data)==null?void 0:i.total)})},V=()=>{t.form={},t.formVisible=!0},C=l=>{t.form=JSON.parse(JSON.stringify(l)),t.formVisible=!0},x=()=>{c.post("/notice/add",t.form).then(l=>{l.code==="200"?(d.success("操作成功"),t.formVisible=!1,r()):d.error(l.msg)})},k=()=>{c.put("/notice/update",t.form).then(l=>{l.code==="200"&&(d.success("操作成功"),t.formVisible=!1,r())})},w=()=>{t.form.id?k():x()},E=l=>{y.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(e=>{c.delete("/notice/delete/"+l).then(i=>{i.code==="200"?(d.success("删除成功"),r()):d.error(i.msg)})}).catch(e=>{console.error(e)})},N=()=>{if(!t.ids.length){d.warning("请选择数据");return}y.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(l=>{c.delete("/notice/delete/batch",{data:t.ids}).then(e=>{e.code==="200"?(d.success("操作成功"),r()):d.error(e.msg)})}).catch(l=>{console.error(l)})},h=l=>{t.ids=l.map(e=>e.id)},v=()=>{t.title=null,r()};return r(),(l,e)=>{const i=P,s=T,m=M,S=$,z=A,f=G,B=q,D=F;return _(),g("div",null,[u("div",K,[o(i,{modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=a=>t.title=a),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入公告标题查询"},null,8,["modelValue"]),o(s,{type:"info",plain:"",onClick:r},{default:n(()=>e[6]||(e[6]=[p("查询")])),_:1,__:[6]}),o(s,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:v},{default:n(()=>e[7]||(e[7]=[p("重置")])),_:1,__:[7]})]),u("div",L,[o(s,{type:"primary",plain:"",onClick:V},{default:n(()=>e[8]||(e[8]=[p("新增")])),_:1,__:[8]}),o(s,{type:"danger",plain:"",onClick:N},{default:n(()=>e[9]||(e[9]=[p("批量删除")])),_:1,__:[9]})]),u("div",H,[o(S,{stripe:"",data:t.tableData,onSelectionChange:h},{default:n(()=>[o(m,{type:"selection",width:"55"}),o(m,{prop:"title",label:"公告标题"}),o(m,{prop:"content",label:"公告内容"}),o(m,{prop:"time",label:"发布时间"}),o(m,{label:"操作",width:"100",fixed:"right"},{default:n(a=>[o(s,{type:"primary",circle:"",icon:b(O),onClick:U=>C(a.row)},null,8,["icon","onClick"]),o(s,{type:"danger",circle:"",icon:b(j),onClick:U=>E(a.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),t.total?(_(),g("div",Q,[o(z,{onCurrentChange:r,background:"",layout:"prev, pager, next","page-size":t.pageSize,"current-page":t.pageNum,"onUpdate:currentPage":e[1]||(e[1]=a=>t.pageNum=a),total:t.total},null,8,["page-size","current-page","total"])])):J("",!0),o(D,{title:"公告信息",modelValue:t.formVisible,"onUpdate:modelValue":e[5]||(e[5]=a=>t.formVisible=a),width:"40%","destroy-on-close":""},{footer:n(()=>[u("span",R,[o(s,{onClick:e[4]||(e[4]=a=>t.formVisible=!1)},{default:n(()=>e[10]||(e[10]=[p("取 消")])),_:1,__:[10]}),o(s,{type:"primary",onClick:w},{default:n(()=>e[11]||(e[11]=[p("确 定")])),_:1,__:[11]})])]),default:n(()=>[o(B,{ref:"form",model:t.form,"label-width":"70px",style:{padding:"20px"}},{default:n(()=>[o(f,{prop:"title",label:"公告标题"},{default:n(()=>[o(i,{modelValue:t.form.title,"onUpdate:modelValue":e[2]||(e[2]=a=>t.form.title=a),placeholder:"请输入公告标题"},null,8,["modelValue"])]),_:1}),o(f,{prop:"content",label:"公告内容"},{default:n(()=>[o(i,{type:"textarea",rows:4,modelValue:t.form.content,"onUpdate:modelValue":e[3]||(e[3]=a=>t.form.content=a),placeholder:"请输入公告内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}};export{ce as default};
