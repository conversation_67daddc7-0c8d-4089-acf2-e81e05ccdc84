/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css              *//* empty css                     *//* empty css            *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                   *//* empty css                     *//* empty css                *//* empty css                     *//* empty css               *//* empty css              */import{r as c}from"./request-BNOh435t.js";/* empty css                    */import{O as R,r as $,c as _,a as f,n as v,b as t,z as q,w as n,A as j,B as G,C as K,D as Q,E as d,o as g,j as u,G as W,m as w,H as X,t as Y,u as b,I as Z,J as ee,K as le,L as te,M as oe,Q as ae,F as ne,x as re,R as ie,S as se,N as x}from"./index-DXyN40AS.js";const de={class:"card",style:{"margin-bottom":"5px"}},pe={class:"card",style:{"margin-bottom":"5px"}},me={class:"card",style:{"margin-bottom":"5px"}},ue={style:{color:"red"}},ce={key:0,class:"card"},fe={class:"dialog-footer"},ge=["innerHTML"],Te={__name:"Product",setup(_e){const C="http://:9090",k=R(),e=$({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),formVisible:!1,form:{},tableData:[],provinceData:[],pageNum:1,pageSize:5,total:0,name:null,ids:[],viewContent:null,viewVisible:null,rules:{name:[{required:!0,message:"请输入景点名称",trigger:"blur"}]}}),p=()=>{c.get("/product/selectPage",{params:{pageNum:e.pageNum,pageSize:e.pageSize,name:e.name}}).then(a=>{var l,r;a.code==="200"&&(e.tableData=((l=a.data)==null?void 0:l.list)||[],e.total=(r=a.data)==null?void 0:r.total)})};(()=>{c.get("province/selectAll").then(a=>{a.code==="200"?e.provinceData=a.data:d.error(a.msg)})})();const E=()=>{e.form={},e.formVisible=!0},h=a=>{e.form=JSON.parse(JSON.stringify(a)),e.formVisible=!0},N=()=>{c.post("/product/add",e.form).then(a=>{a.code==="200"?(d.success("操作成功"),e.formVisible=!1,p()):d.error(a.msg)})},S=()=>{c.put("/product/update",e.form).then(a=>{a.code==="200"&&(d.success("操作成功"),e.formVisible=!1,p())})},U=()=>{e.form.id?S():N()},I=a=>{x.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(l=>{c.delete("/product/delete/"+a).then(r=>{r.code==="200"?(d.success("删除成功"),p()):d.error(r.msg)})}).catch(l=>{console.error(l)})},D=()=>{if(!e.ids.length){d.warning("请选择数据");return}x.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(a=>{c.delete("/product/delete/batch",{data:e.ids}).then(l=>{l.code==="200"?(d.success("操作成功"),p()):d.error(l.msg)})}).catch(a=>{console.error(a)})},B=a=>{e.ids=a.map(l=>l.id)},z=()=>{e.name=null,p()},P=a=>{e.form.img=a.data};return p(),(a,l)=>{const r=q,i=j,s=W,M=X,O=G,T=K,m=te,F=oe,J=ie,L=ae,V=se,A=le,y=Q;return g(),_("div",null,[f("div",de,[t(r,{modelValue:e.name,"onUpdate:modelValue":l[0]||(l[0]=o=>e.name=o),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入特产名称查询"},null,8,["modelValue"]),t(i,{type:"info",plain:"",onClick:p},{default:n(()=>l[11]||(l[11]=[u("查询")])),_:1,__:[11]}),t(i,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:z},{default:n(()=>l[12]||(l[12]=[u("重置")])),_:1,__:[12]})]),f("div",pe,[t(i,{type:"primary",plain:"",onClick:E},{default:n(()=>l[13]||(l[13]=[u("新增")])),_:1,__:[13]}),t(i,{type:"danger",plain:"",onClick:D},{default:n(()=>l[14]||(l[14]=[u("批量删除")])),_:1,__:[14]})]),f("div",me,[t(O,{stripe:"",data:e.tableData,onSelectionChange:B,"tooltip-effect":"light myEffect"},{default:n(()=>[t(s,{type:"selection",width:"55"}),t(s,{prop:"img",label:"特产图片",width:"80"},{default:n(o=>[o.row.img?(g(),w(M,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:o.row.img,"preview-src-list":[o.row.img],"preview-teleported":""},null,8,["src","preview-src-list"])):v("",!0)]),_:1}),t(s,{prop:"name",label:"特产名称"}),t(s,{prop:"description",label:"特产简介",width:"200","show-overflow-tooltip":""}),t(s,{prop:"provinceName",label:"所属省份"}),t(s,{prop:"price",label:"价格"},{default:n(o=>[f("span",ue,"￥"+Y(o.row.price),1)]),_:1}),t(s,{prop:"num",label:"剩余量"}),t(s,{prop:"unit",label:"计量单位"}),t(s,{label:"操作",width:"100",fixed:"right"},{default:n(o=>[t(i,{type:"primary",circle:"",icon:b(Z),onClick:H=>h(o.row)},null,8,["icon","onClick"]),t(i,{type:"danger",circle:"",icon:b(ee),onClick:H=>I(o.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),e.total?(g(),_("div",ce,[t(T,{onCurrentChange:p,background:"",layout:"prev, pager, next","page-size":e.pageSize,"current-page":e.pageNum,"onUpdate:currentPage":l[1]||(l[1]=o=>e.pageNum=o),total:e.total},null,8,["page-size","current-page","total"])])):v("",!0),t(y,{title:"省份特产信息",modelValue:e.formVisible,"onUpdate:modelValue":l[9]||(l[9]=o=>e.formVisible=o),width:"40%","destroy-on-close":""},{footer:n(()=>[f("span",fe,[t(i,{onClick:l[8]||(l[8]=o=>e.formVisible=!1)},{default:n(()=>l[16]||(l[16]=[u("取 消")])),_:1,__:[16]}),t(i,{type:"primary",onClick:U},{default:n(()=>l[17]||(l[17]=[u("确 定")])),_:1,__:[17]})])]),default:n(()=>[t(A,{ref_key:"formRef",ref:k,model:e.form,rules:e.rules,"label-width":"100px",style:{padding:"20px"}},{default:n(()=>[t(m,{prop:"name",label:"特产名称"},{default:n(()=>[t(r,{modelValue:e.form.name,"onUpdate:modelValue":l[2]||(l[2]=o=>e.form.name=o),placeholder:"请输入特产名称"},null,8,["modelValue"])]),_:1}),t(m,{prop:"img",label:"特产图片"},{default:n(()=>[t(F,{action:b(C)+"/files/upload","on-success":P,"list-type":"picture"},{default:n(()=>[t(i,{type:"primary"},{default:n(()=>l[15]||(l[15]=[u("上传特产图片")])),_:1,__:[15]})]),_:1},8,["action"])]),_:1}),t(m,{prop:"description",label:"特产简介"},{default:n(()=>[t(r,{type:"textarea",rows:4,modelValue:e.form.description,"onUpdate:modelValue":l[3]||(l[3]=o=>e.form.description=o),placeholder:"请输入特产简介"},null,8,["modelValue"])]),_:1}),t(m,{prop:"provinceId",label:"所属省份"},{default:n(()=>[t(L,{modelValue:e.form.provinceId,"onUpdate:modelValue":l[4]||(l[4]=o=>e.form.provinceId=o),placeholder:"请选择省份",style:{width:"100%"}},{default:n(()=>[(g(!0),_(ne,null,re(e.provinceData,o=>(g(),w(J,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(m,{prop:"price",label:"特产价格"},{default:n(()=>[t(V,{modelValue:e.form.price,"onUpdate:modelValue":l[5]||(l[5]=o=>e.form.price=o),min:1},null,8,["modelValue"])]),_:1}),t(m,{prop:"num",label:"剩余数量"},{default:n(()=>[t(V,{modelValue:e.form.num,"onUpdate:modelValue":l[6]||(l[6]=o=>e.form.num=o),min:0},null,8,["modelValue"])]),_:1}),t(m,{prop:"unit",label:"计量单位"},{default:n(()=>[t(r,{modelValue:e.form.unit,"onUpdate:modelValue":l[7]||(l[7]=o=>e.form.unit=o),placeholder:"请输入计量单位"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),t(y,{title:"详细介绍",modelValue:e.viewVisible,"onUpdate:modelValue":l[10]||(l[10]=o=>e.viewVisible=o),width:"50%","destroy-on-close":""},{default:n(()=>[f("div",{innerHTML:e.viewContent,style:{padding:"20px"}},null,8,ge)]),_:1},8,["modelValue"])])}}};export{Te as default};
