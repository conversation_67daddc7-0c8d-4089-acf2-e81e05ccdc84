/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css                     *//* empty css               */import{r as f}from"./request-BNOh435t.js";/* empty css                    */import{r as T,c as b,a as c,n as V,b as t,z as $,w as r,A,B as O,C as j,D as q,o as _,j as m,G,m as H,H as K,u as g,I as L,J as Q,K as R,L as W,M as X,N as y,E as d}from"./index-DXyN40AS.js";const Y={class:"card",style:{"margin-bottom":"5px"}},Z={class:"card",style:{"margin-bottom":"5px"}},ee={class:"card",style:{"margin-bottom":"5px"}},le={key:0,class:"card"},te={class:"dialog-footer"},we={__name:"User",setup(oe){const v="http://:9090",l=T({formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,name:null,ids:[]}),i=()=>{f.get("/user/selectPage",{params:{pageNum:l.pageNum,pageSize:l.pageSize,name:l.name}}).then(a=>{var e,n;a.code==="200"&&(l.tableData=((e=a.data)==null?void 0:e.list)||[],l.total=(n=a.data)==null?void 0:n.total)})},w=()=>{l.form={},l.formVisible=!0},x=a=>{l.form=JSON.parse(JSON.stringify(a)),l.formVisible=!0},C=()=>{f.post("/user/add",l.form).then(a=>{a.code==="200"?(d.success("操作成功"),l.formVisible=!1,i()):d.error(a.msg)})},h=()=>{f.put("/user/update",l.form).then(a=>{a.code==="200"&&(d.success("操作成功"),l.formVisible=!1,i())})},k=()=>{l.form.id?h():C()},E=a=>{y.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(e=>{f.delete("/user/delete/"+a).then(n=>{n.code==="200"?(d.success("删除成功"),i()):d.error(n.msg)})}).catch(e=>{console.error(e)})},N=()=>{if(!l.ids.length){d.warning("请选择数据");return}y.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(a=>{f.delete("/user/delete/batch",{data:l.ids}).then(e=>{e.code==="200"?(d.success("操作成功"),i()):d.error(e.msg)})}).catch(a=>{console.error(a)})},U=a=>{l.ids=a.map(e=>e.id)},S=a=>{l.form.avatar=a.data},B=()=>{l.name=null,i()};return i(),(a,e)=>{const n=$,s=A,p=G,z=K,D=O,I=j,u=W,F=X,J=R,M=q;return _(),b("div",null,[c("div",Y,[t(n,{modelValue:l.name,"onUpdate:modelValue":e[0]||(e[0]=o=>l.name=o),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入名称查询"},null,8,["modelValue"]),t(s,{type:"info",plain:"",onClick:i},{default:r(()=>e[8]||(e[8]=[m("查询")])),_:1,__:[8]}),t(s,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:B},{default:r(()=>e[9]||(e[9]=[m("重置")])),_:1,__:[9]})]),c("div",Z,[t(s,{type:"primary",plain:"",onClick:w},{default:r(()=>e[10]||(e[10]=[m("新增")])),_:1,__:[10]}),t(s,{type:"danger",plain:"",onClick:N},{default:r(()=>e[11]||(e[11]=[m("批量删除")])),_:1,__:[11]})]),c("div",ee,[t(D,{stripe:"",data:l.tableData,onSelectionChange:U},{default:r(()=>[t(p,{type:"selection",width:"55"}),t(p,{prop:"username",label:"账号"}),t(p,{prop:"avatar",label:"头像"},{default:r(o=>[o.row.avatar?(_(),H(z,{key:0,style:{width:"40px",height:"40px","border-radius":"50%",display:"block"},src:o.row.avatar,"preview-src-list":[o.row.avatar],"preview-teleported":""},null,8,["src","preview-src-list"])):V("",!0)]),_:1}),t(p,{prop:"name",label:"姓名"}),t(p,{prop:"role",label:"角色"}),t(p,{prop:"phone",label:"电话"}),t(p,{prop:"email",label:"邮箱"}),t(p,{label:"操作",width:"100",fixed:"right"},{default:r(o=>[t(s,{type:"primary",circle:"",icon:g(L),onClick:P=>x(o.row)},null,8,["icon","onClick"]),t(s,{type:"danger",circle:"",icon:g(Q),onClick:P=>E(o.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),l.total?(_(),b("div",le,[t(I,{onCurrentChange:i,background:"",layout:"prev, pager, next","page-size":l.pageSize,"current-page":l.pageNum,"onUpdate:currentPage":e[1]||(e[1]=o=>l.pageNum=o),total:l.total},null,8,["page-size","current-page","total"])])):V("",!0),t(M,{title:"用户信息",modelValue:l.formVisible,"onUpdate:modelValue":e[7]||(e[7]=o=>l.formVisible=o),width:"40%","destroy-on-close":""},{footer:r(()=>[c("span",te,[t(s,{onClick:e[6]||(e[6]=o=>l.formVisible=!1)},{default:r(()=>e[13]||(e[13]=[m("取 消")])),_:1,__:[13]}),t(s,{type:"primary",onClick:k},{default:r(()=>e[14]||(e[14]=[m("确 定")])),_:1,__:[14]})])]),default:r(()=>[t(J,{ref:"form",model:l.form,"label-width":"70px",style:{padding:"20px"}},{default:r(()=>[t(u,{prop:"username",label:"用户名"},{default:r(()=>[t(n,{modelValue:l.form.username,"onUpdate:modelValue":e[2]||(e[2]=o=>l.form.username=o),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),t(u,{prop:"avatar",label:"头像"},{default:r(()=>[t(F,{action:g(v)+"/files/upload","on-success":S,"list-type":"picture"},{default:r(()=>[t(s,{type:"primary"},{default:r(()=>e[12]||(e[12]=[m("点击上传")])),_:1,__:[12]})]),_:1},8,["action"])]),_:1}),t(u,{prop:"name",label:"姓名"},{default:r(()=>[t(n,{modelValue:l.form.name,"onUpdate:modelValue":e[3]||(e[3]=o=>l.form.name=o),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),t(u,{prop:"phone",label:"电话"},{default:r(()=>[t(n,{modelValue:l.form.phone,"onUpdate:modelValue":e[4]||(e[4]=o=>l.form.phone=o),placeholder:"请输入电话"},null,8,["modelValue"])]),_:1}),t(u,{prop:"email",label:"邮箱"},{default:r(()=>[t(n,{modelValue:l.form.email,"onUpdate:modelValue":e[5]||(e[5]=o=>l.form.email=o),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}};export{we as default};
