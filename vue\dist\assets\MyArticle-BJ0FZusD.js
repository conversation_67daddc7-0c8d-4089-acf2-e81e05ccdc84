/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css                     *//* empty css               */import{r as c}from"./request-BNOh435t.js";/* empty css                    */import{r as q,O as A,c as x,a as u,n as _,b as l,t as g,z as F,w as a,A as J,B as R,C as j,D as G,o as f,j as r,G as K,m as y,H as Q,P as W,K as X,L as Y,M as Z,u as ee,N as k,E as m}from"./index-DXyN40AS.js";const te={style:{margin:"20px auto",width:"80%","min-height":"600px"}},le={style:{color:"darkorchid","font-size":"18px","margin-top":"20px"}},oe={class:"card",style:{"margin-bottom":"5px"}},ae={class:"card",style:{"margin-bottom":"5px"}},ie={class:"card",style:{"margin-bottom":"5px"}},re={key:0,class:"card"},se={class:"dialog-footer"},ne=["innerHTML"],Ne={__name:"MyArticle",setup(de){const C="http://:9090",t=q({formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,viewContent:null,viewVisible:null,title:null,status:null,ids:[],rules:{title:[{required:!0,message:"请输入帖子名称",trigger:"blur"}]}}),h=A(),d=()=>{c.get("/article/selectPage",{params:{pageNum:t.pageNum,pageSize:t.pageSize,title:t.title,status:t.status}}).then(i=>{var e,p;i.code==="200"&&(t.tableData=((e=i.data)==null?void 0:e.list)||[],t.total=(p=i.data)==null?void 0:p.total)})},E=()=>{if(!t.ids.length){m.warning("请选择数据");return}k.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(i=>{c.delete("/article/delete/batch",{data:t.ids}).then(e=>{e.code==="200"?(m.success("操作成功"),d()):m.error(e.msg)})}).catch(i=>{console.error(i)})},N=()=>{c.post("/article/add",t.form).then(i=>{i.code==="200"?(m.success("操作成功"),t.formVisible=!1,d()):m.error(i.msg)})},S=i=>{t.form.img=i.data},z=i=>{t.form=JSON.parse(JSON.stringify(i)),t.formVisible=!0},B=()=>{c.put("/article/update",t.form).then(i=>{i.code==="200"&&(m.success("操作成功"),t.formVisible=!1,d())})},U=()=>{t.form.id?B():N()},D=i=>{t.ids=i.map(e=>e.id)},M=i=>{k.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(e=>{c.delete("/article/delete/"+i).then(p=>{p.code==="200"?(m.success("删除成功"),d()):m.error(p.msg)})}).catch(e=>{console.error(e)})},T=()=>{t.title=null,t.status=null,d()},I=i=>{t.viewContent=i,t.viewVisible=!0};return d(),(i,e)=>{const p=F,s=J,n=K,P=Q,w=W,$=R,H=j,L=Z,b=Y,O=X,v=G;return f(),x("div",te,[u("div",le,"我的帖子 （"+g(t.total)+"）",1),u("div",oe,[l(p,{modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=o=>t.title=o),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入帖子标题查询"},null,8,["modelValue"]),l(s,{type:"info",plain:"",onClick:d},{default:a(()=>e[6]||(e[6]=[r("查询")])),_:1,__:[6]}),l(s,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:T},{default:a(()=>e[7]||(e[7]=[r("重置")])),_:1,__:[7]})]),u("div",ae,[l(s,{type:"danger",plain:"",onClick:E},{default:a(()=>e[8]||(e[8]=[r("批量删除")])),_:1,__:[8]})]),u("div",ie,[l($,{stripe:"",data:t.tableData,onSelectionChange:D,"tooltip-effect":"light myEffect"},{default:a(()=>[l(n,{type:"selection",width:"55"}),l(n,{prop:"img",label:"帖子封面",width:"80"},{default:a(o=>[o.row.img?(f(),y(P,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:o.row.img,"preview-src-list":[o.row.img],"preview-teleported":""},null,8,["src","preview-src-list"])):_("",!0)]),_:1}),l(n,{prop:"title",label:"帖子标题","show-overflow-tooltip":""}),l(n,{prop:"content",label:"帖子内容"},{default:a(o=>[l(s,{type:"primary",onClick:V=>I(o.row.content)},{default:a(()=>e[9]||(e[9]=[r("查看详情")])),_:2,__:[9]},1032,["onClick"])]),_:1}),l(n,{prop:"userName",label:"发布人"}),l(n,{prop:"time",label:"发布时间"}),l(n,{prop:"views",label:"浏览量"}),l(n,{prop:"status",label:"帖子状态"},{default:a(o=>[o.row.status==="待审核"?(f(),y(w,{key:0,type:"warning"},{default:a(()=>[r(g(o.row.status),1)]),_:2},1024)):_("",!0),o.row.status==="通过"?(f(),y(w,{key:1,type:"success"},{default:a(()=>[r(g(o.row.status),1)]),_:2},1024)):_("",!0),o.row.status==="拒绝"?(f(),y(w,{key:2,type:"danger"},{default:a(()=>[r(g(o.row.status),1)]),_:2},1024)):_("",!0)]),_:1}),l(n,{label:"操作",width:"200",fixed:"right"},{default:a(o=>[l(s,{type:"primary",onClick:V=>z(o.row)},{default:a(()=>e[10]||(e[10]=[r("编辑")])),_:2,__:[10]},1032,["onClick"]),l(s,{type:"danger",onClick:V=>M(o.row.id)},{default:a(()=>e[11]||(e[11]=[r("删除")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),t.total?(f(),x("div",re,[l(H,{onCurrentChange:d,background:"",layout:"prev, pager, next","page-size":t.pageSize,"current-page":t.pageNum,"onUpdate:currentPage":e[1]||(e[1]=o=>t.pageNum=o),total:t.total},null,8,["page-size","current-page","total"])])):_("",!0),l(v,{title:"帖子信息",modelValue:t.formVisible,"onUpdate:modelValue":e[4]||(e[4]=o=>t.formVisible=o),width:"50%","destroy-on-close":""},{footer:a(()=>[u("span",se,[l(s,{onClick:e[3]||(e[3]=o=>t.formVisible=!1)},{default:a(()=>e[13]||(e[13]=[r("取 消")])),_:1,__:[13]}),l(s,{type:"primary",onClick:U},{default:a(()=>e[14]||(e[14]=[r("确 定")])),_:1,__:[14]})])]),default:a(()=>[l(O,{ref_key:"formRef",ref:h,model:t.form,rules:t.rules,"label-width":"110px",style:{padding:"20px"}},{default:a(()=>[l(b,{prop:"img",label:"封面"},{default:a(()=>[l(L,{action:ee(C)+"/files/upload","on-success":S,"list-type":"picture"},{default:a(()=>[l(s,{type:"primary"},{default:a(()=>e[12]||(e[12]=[r("上传封面")])),_:1,__:[12]})]),_:1},8,["action"])]),_:1}),l(b,{prop:"title",label:"帖子名称"},{default:a(()=>[l(p,{modelValue:t.form.title,"onUpdate:modelValue":e[2]||(e[2]=o=>t.form.title=o),placeholder:"请输入帖子名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),l(v,{title:"帖子内容",modelValue:t.viewVisible,"onUpdate:modelValue":e[5]||(e[5]=o=>t.viewVisible=o),width:"50%","destroy-on-close":""},{default:a(()=>[u("div",{innerHTML:t.viewContent,style:{padding:"20px"}},null,8,ne)]),_:1},8,["modelValue"])])}}};export{Ne as default};
