/* empty css                *//* empty css               *//* empty css                  *//* empty css              */import{r as _}from"./request-BNOh435t.js";import{O as c,r as g,c as x,a,b as o,w as r,K as b,A as V,o as y,L as v,z as k,j as q,E as n}from"./index-DXyN40AS.js";const E={style:{width:"50%",margin:"20px auto","min-height":"600px"}},w={class:"card"},h={style:{"text-align":"center"}},R={__name:"Feedback",setup(B){const m=c(),t=g({form:{},rules:{title:[{required:!0,message:"请输入标题",trigger:"blur"}],question:[{required:!0,message:"请输入问题",trigger:"blur"}]}}),u=()=>{m.value.validate(i=>{i&&_.post("/feedback/add",t.form).then(e=>{e.code==="200"?(n.success("提交成功，等待管理端进行回复"),t.form={}):n.error(e.msg)})})};return(i,e)=>{const s=k,d=v,p=b,f=V;return y(),x("div",E,[a("div",w,[e[4]||(e[4]=a("div",{style:{color:"lightskyblue","font-size":"23px","text-align":"center"}},"提交您的反馈信息",-1)),a("div",null,[o(p,{ref_key:"formRef",ref:m,model:t.form,rules:t.rules,"label-width":"70px",style:{padding:"20px"}},{default:r(()=>[o(d,{prop:"title",label:"标题"},{default:r(()=>[o(s,{modelValue:t.form.title,"onUpdate:modelValue":e[0]||(e[0]=l=>t.form.title=l),placeholder:"请输入反馈信息标题"},null,8,["modelValue"])]),_:1}),o(d,{prop:"question",label:"问题"},{default:r(()=>[o(s,{type:"textarea",rows:4,modelValue:t.form.question,"onUpdate:modelValue":e[1]||(e[1]=l=>t.form.question=l),placeholder:"请输入反馈内容"},null,8,["modelValue"])]),_:1}),o(d,{prop:"idea",label:"想法"},{default:r(()=>[o(s,{type:"textarea",rows:4,modelValue:t.form.idea,"onUpdate:modelValue":e[2]||(e[2]=l=>t.form.idea=l),placeholder:"请输入您的想法"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),a("div",h,[o(f,{type:"primary",onClick:u},{default:r(()=>e[3]||(e[3]=[q("提交")])),_:1,__:[3]})])])])}}};export{R as default};
