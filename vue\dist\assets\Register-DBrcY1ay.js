/* empty css                *//* empty css                  *//* empty css               *//* empty css            *//* empty css               *//* empty css                  *//* empty css              */import{_ as v,r as E,O as b,c as z,a as n,b as o,w as s,K as U,o as y,L as P,z as R,u as i,a9 as u,aa as f,Q as B,R as I,A as S,j as _,E as g}from"./index-DXyN40AS.js";import{r as q}from"./request-BNOh435t.js";const N={class:"login-container"},k={class:"login-box"},C={__name:"Register",setup(F){const r=E({form:{},rules:{username:[{required:!0,message:"请输入账号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],role:[{required:!0,message:"请选择用户注册类型",trigger:"blur"}],confirmPassword:[{validator:(m,e,t)=>{e?(e!==r.form.password&&t(new Error("确认密码跟原密码不一致!")),t()):t(new Error("请确认密码"))},trigger:"blur"}]}}),d=b(),c=()=>{d.value.validate(m=>{m&&q.post("/register",r.form).then(e=>{e.code==="200"?(g.success("注册成功"),setInterval(()=>{location.href="/login"},500)):g.error(e.msg)})})};return(m,e)=>{const t=R,a=P,p=I,w=B,x=S,V=U;return y(),z("div",N,[n("div",k,[e[7]||(e[7]=n("div",{style:{"font-weight":"bold","font-size":"24px","text-align":"center","margin-bottom":"30px",color:"#1450aa"}},"欢 迎 注 册",-1)),o(V,{ref_key:"formRef",ref:d,model:r.form,rules:r.rules},{default:s(()=>[o(a,{prop:"username"},{default:s(()=>[o(t,{"prefix-icon":i(u),size:"large",modelValue:r.form.username,"onUpdate:modelValue":e[0]||(e[0]=l=>r.form.username=l),placeholder:"请输入账号"},null,8,["prefix-icon","modelValue"])]),_:1}),o(a,{prop:"name"},{default:s(()=>[o(t,{"prefix-icon":i(u),size:"large",modelValue:r.form.name,"onUpdate:modelValue":e[1]||(e[1]=l=>r.form.name=l),placeholder:"请输入姓名"},null,8,["prefix-icon","modelValue"])]),_:1}),o(a,{prop:"password"},{default:s(()=>[o(t,{"show-password":"","prefix-icon":i(f),size:"large",modelValue:r.form.password,"onUpdate:modelValue":e[2]||(e[2]=l=>r.form.password=l),placeholder:"请输入密码"},null,8,["prefix-icon","modelValue"])]),_:1}),o(a,{prop:"confirmPassword"},{default:s(()=>[o(t,{"show-password":"","prefix-icon":i(f),size:"large",modelValue:r.form.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=l=>r.form.confirmPassword=l),placeholder:"请确认密码"},null,8,["prefix-icon","modelValue"])]),_:1}),o(a,{prop:"role"},{default:s(()=>[o(w,{size:"large",modelValue:r.form.role,"onUpdate:modelValue":e[4]||(e[4]=l=>r.form.role=l)},{default:s(()=>[o(p,{value:"BUSINESS",label:"商家"}),o(p,{value:"USER",label:"用户"})]),_:1},8,["modelValue"])]),_:1}),o(a,null,{default:s(()=>[o(x,{size:"large",type:"primary",style:{width:"100%"},onClick:c},{default:s(()=>e[5]||(e[5]=[_("注 册")])),_:1,__:[5]})]),_:1}),e[6]||(e[6]=n("div",{style:{"text-align":"right"}},[_(" 已有账号？请 "),n("a",{href:"/login"},"登录")],-1))]),_:1,__:[6]},8,["model","rules"])])])}}},G=v(C,[["__scopeId","data-v-9cb1d696"]]);export{G as default};
