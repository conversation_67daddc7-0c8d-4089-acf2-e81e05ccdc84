/* empty css                *//* empty css                  *//* empty css               *//* empty css              */import{r as c}from"./request-BNOh435t.js";import{O as g,r as P,c as x,a as u,b as o,w as a,K as V,o as v,L as E,z as b,A as y,j as h,E as m,f as B}from"./index-DXyN40AS.js";const I={style:{width:"50%",margin:"20px auto"}},N={class:"card"},q={style:{"text-align":"center"}},R={__name:"Password",setup(S){const n=g(),p=(l,e,r)=>{e?(e!==s.user.newPassword&&r(new Error("确认密码跟原密码不一致!")),r()):r(new Error("请确认密码"))},s=P({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),rules:{password:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{validator:p,trigger:"blur"}]}}),i=()=>{n.value.validate(l=>{l&&c.put("/updatePassword",s.user).then(e=>{e.code==="200"?(m.success("保存成功"),w()):m.error(e.msg)})})},w=()=>{localStorage.removeItem("xm-user"),B.push("/login")};return(l,e)=>{const r=b,d=E,f=y,_=V;return v(),x("div",I,[e[4]||(e[4]=u("div",{style:{"font-size":"18px","margin-bottom":"20px"}},"修改密码",-1)),u("div",N,[o(_,{ref_key:"formRef",ref:n,rules:s.rules,model:s.user,"label-width":"80px",style:{padding:"20px"}},{default:a(()=>[o(d,{label:"原密码",prop:"password"},{default:a(()=>[o(r,{modelValue:s.user.password,"onUpdate:modelValue":e[0]||(e[0]=t=>s.user.password=t),placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),o(d,{label:"新密码",prop:"newPassword"},{default:a(()=>[o(r,{modelValue:s.user.newPassword,"onUpdate:modelValue":e[1]||(e[1]=t=>s.user.newPassword=t),placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(d,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[o(r,{modelValue:s.user.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=t=>s.user.confirmPassword=t),placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]),_:1}),u("div",q,[o(f,{type:"primary",onClick:i},{default:a(()=>e[3]||(e[3]=[h("保 存")])),_:1,__:[3]})])]),_:1},8,["rules","model"])])])}}};export{R as default};
