/* empty css                *//* empty css                  *//* empty css               *//* empty css            *//* empty css              *//* empty css                     *//* empty css               *//* empty css                  *//* empty css                 */import{r as k}from"./request-BNOh435t.js";import{r as D,c as p,a as U,b as l,w as a,K as A,E as g,h as L,o as n,L as O,M as J,u as v,m as u,k as M,z as T,Q as j,F as q,x as z,R as K,S as Q,n as y,P as R,j as i,t as b,A as G}from"./index-DXyN40AS.js";const H={style:{width:"50%"},class:"card"},W=["src"],X=["src"],Y=["src"],Z={style:{"text-align":"center"}},ce={__name:"Authentication",emits:["updateUser"],setup($,{emit:E}){const c="http://:9090",e=D({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),provinceData:[]}),x=s=>{e.user.img=s.data},w=s=>{e.user.license=s.data},I=s=>{e.user.front=s.data};(()=>{k.get("province/selectAll").then(s=>{s.code==="200"?e.provinceData=s.data:g.error(s.msg)})})();const S=E,h=()=>{e.user.role==="BUSINESS"&&(e.user.status="待审核",k.put("/business/update",e.user).then(s=>{s.code==="200"?(g.success("提交成功，等待管理员审核"),localStorage.setItem("xm-user",JSON.stringify(e.user)),S("updateUser")):g.error(s.msg)}))};return(s,t)=>{const m=L("Plus"),_=M,f=J,r=O,d=T,N=K,B=j,F=Q,V=R,P=G,C=A;return n(),p("div",H,[t[9]||(t[9]=U("div",{style:{color:"red",padding:"10px"}},"认证信息修改，账号将进入审核状态，直至管理员审核完成",-1)),l(C,{ref:"user",model:e.user,"label-width":"90px",style:{padding:"20px"}},{default:a(()=>[l(r,{prop:"img",label:"宾馆头像"},{default:a(()=>[l(f,{action:v(c)+"/files/upload","on-success":x,"show-file-list":!1,class:"avatar-uploader"},{default:a(()=>[e.user.img?(n(),p("img",{key:0,src:e.user.img,class:"avatar"},null,8,W)):(n(),u(_,{key:1,class:"avatar-uploader-icon"},{default:a(()=>[l(m)]),_:1}))]),_:1},8,["action"])]),_:1}),l(r,{prop:"name",label:"宾馆名称"},{default:a(()=>[l(d,{modelValue:e.user.name,"onUpdate:modelValue":t[0]||(t[0]=o=>e.user.name=o),placeholder:"请输入宾馆名称"},null,8,["modelValue"])]),_:1}),l(r,{prop:"provinceId",label:"所属省份"},{default:a(()=>[l(B,{modelValue:e.user.provinceId,"onUpdate:modelValue":t[1]||(t[1]=o=>e.user.provinceId=o),placeholder:"请选择省份",style:{width:"100%"}},{default:a(()=>[(n(!0),p(q,null,z(e.provinceData,o=>(n(),u(N,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"content",label:"描述"},{default:a(()=>[l(d,{type:"textarea",rows:6,modelValue:e.user.content,"onUpdate:modelValue":t[2]||(t[2]=o=>e.user.content=o),placeholder:"请输入宾馆描述"},null,8,["modelValue"])]),_:1}),l(r,{prop:"address",label:"地址"},{default:a(()=>[l(d,{type:"textarea",rows:4,modelValue:e.user.address,"onUpdate:modelValue":t[3]||(t[3]=o=>e.user.address=o),placeholder:"请输入宾馆地址"},null,8,["modelValue"])]),_:1}),l(r,{prop:"price",label:"价格"},{default:a(()=>[l(F,{modelValue:e.user.price,"onUpdate:modelValue":t[4]||(t[4]=o=>e.user.price=o),min:100},null,8,["modelValue"])]),_:1}),l(r,{prop:"link",label:"链接"},{default:a(()=>[l(d,{modelValue:e.user.link,"onUpdate:modelValue":t[5]||(t[5]=o=>e.user.link=o),placeholder:"请输入官网链接"},null,8,["modelValue"])]),_:1}),l(r,{prop:"license",label:"营业执照"},{default:a(()=>[l(f,{action:v(c)+"/files/upload","on-success":w,"show-file-list":!1,class:"avatar-uploader"},{default:a(()=>[e.user.license?(n(),p("img",{key:0,src:e.user.license,class:"avatar"},null,8,X)):(n(),u(_,{key:1,class:"avatar-uploader-icon"},{default:a(()=>[l(m)]),_:1}))]),_:1},8,["action"])]),_:1}),l(r,{prop:"leader",label:"负责人姓名"},{default:a(()=>[l(d,{modelValue:e.user.leader,"onUpdate:modelValue":t[6]||(t[6]=o=>e.user.leader=o),placeholder:"请输入负责人姓名"},null,8,["modelValue"])]),_:1}),l(r,{prop:"code",label:"身份证号"},{default:a(()=>[l(d,{modelValue:e.user.code,"onUpdate:modelValue":t[7]||(t[7]=o=>e.user.code=o),placeholder:"请输入负责人身份证号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"front",label:"身份证正面"},{default:a(()=>[l(f,{action:v(c)+"/files/upload","on-success":I,"show-file-list":!1,class:"avatar-uploader"},{default:a(()=>[e.user.front?(n(),p("img",{key:0,src:e.user.front,class:"avatar"},null,8,Y)):(n(),u(_,{key:1,class:"avatar-uploader-icon"},{default:a(()=>[l(m)]),_:1}))]),_:1},8,["action"])]),_:1}),l(r,{prop:"name",label:"宾馆名称"},{default:a(()=>[e.user.status==="待审核"?(n(),u(V,{key:0,type:"warning"},{default:a(()=>[i(b(e.user.status),1)]),_:1})):y("",!0),e.user.status==="通过"?(n(),u(V,{key:1,type:"success"},{default:a(()=>[i(b(e.user.status),1)]),_:1})):y("",!0),e.user.status==="拒绝"?(n(),u(V,{key:2,type:"danger"},{default:a(()=>[i(b(e.user.status),1)]),_:1})):y("",!0)]),_:1}),U("div",Z,[l(P,{type:"primary",onClick:h},{default:a(()=>t[8]||(t[8]=[i("提交修改申请")])),_:1,__:[8]})])]),_:1},8,["model"])])}}};export{ce as default};
