/* empty css                *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as m}from"./request-BNOh435t.js";/* empty css                    *//* empty css                */import{r as h,c,a as u,n as y,b as a,z as C,w as i,A as V,B as w,C as E,o as d,j as g,G as k,u as v,J as z,N as B,E as _}from"./index-DXyN40AS.js";const S={class:"card",style:{"margin-bottom":"5px"}},D={class:"card",style:{"margin-bottom":"5px"}},P={key:0,class:"card"},Q={__name:"Comment",setup(T){const e=h({formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,content:null,userName:null}),r=()=>{m.get("/comment/selectPage",{params:{pageNum:e.pageNum,pageSize:e.pageSize,content:e.content,userName:e.userName}}).then(n=>{var t,o;n.code==="200"&&(e.tableData=((t=n.data)==null?void 0:t.list)||[],e.total=(o=n.data)==null?void 0:o.total)})},f=n=>{B.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(t=>{m.delete("/comment/delete/"+n).then(o=>{o.code==="200"?(_.success("删除成功"),r()):_.error(o.msg)})}).catch(t=>{console.error(t)})},b=()=>{e.content=null,e.userName=null,r()};return r(),(n,t)=>{const o=C,p=V,s=k,N=w,x=E;return d(),c("div",null,[u("div",S,[a(o,{modelValue:e.content,"onUpdate:modelValue":t[0]||(t[0]=l=>e.content=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入评论内容查询"},null,8,["modelValue"]),a(o,{modelValue:e.userName,"onUpdate:modelValue":t[1]||(t[1]=l=>e.userName=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入评论人查询"},null,8,["modelValue"]),a(p,{type:"info",plain:"",onClick:r},{default:i(()=>t[3]||(t[3]=[g("查询")])),_:1,__:[3]}),a(p,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:b},{default:i(()=>t[4]||(t[4]=[g("重置")])),_:1,__:[4]})]),u("div",D,[a(N,{stripe:"",data:e.tableData},{default:i(()=>[a(s,{prop:"userName",label:"评论人"}),a(s,{prop:"articleName",label:"帖子名称","show-overflow-tooltip":""}),a(s,{prop:"content",label:"评论内容"}),a(s,{prop:"time",label:"评论发布时间"}),a(s,{label:"操作",width:"100",fixed:"right"},{default:i(l=>[a(p,{type:"danger",circle:"",icon:v(z),onClick:U=>f(l.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),e.total?(d(),c("div",P,[a(x,{onCurrentChange:r,background:"",layout:"prev, pager, next","page-size":e.pageSize,"current-page":e.pageNum,"onUpdate:currentPage":t[2]||(t[2]=l=>e.pageNum=l),total:e.total},null,8,["page-size","current-page","total"])])):y("",!0)])}}};export{Q as default};
