/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                     *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as b}from"./request-BNOh435t.js";/* empty css                    */import{r as J,c as V,a as g,n as d,b as l,t as f,z as I,w as a,Q as R,A as F,B as G,C as M,D as j,E as c,o as i,R as q,j as n,G as K,m as u,P as L,K as Q,L as H,T as W,U as X,N as x}from"./index-DXyN40AS.js";const Y={style:{width:"80%",margin:"20px auto","min-height":"600px"}},Z={style:{"font-size":"18px"}},ee={class:"card",style:{margin:"20px 0"}},te={class:"card",style:{"margin-bottom":"5px"}},oe={style:{color:"red"}},le={key:0,class:"card"},ae={style:{color:"red"}},re={class:"dialog-footer"},Ve={__name:"ProductOrders",setup(se){const t=J({orderNo:null,status:null,total:0,pageNum:1,pageSize:5,ordersData:[],form:{},type:"aliPay",formVisible:!1}),C=r=>{t.form=JSON.parse(JSON.stringify(r)),t.formVisible=!0},E=()=>{b.put("/productOrders/pay",t.form).then(r=>{r.code==="200"?(c.success("下单成功"),t.formVisible=!1,m()):c.error(r.msg)})},h=r=>{x.confirm("取消后订单无法恢复，您确定取消吗？","取消订单确认",{type:"warning"}).then(e=>{b.put("/productOrders/cancel",r).then(p=>{p.code==="200"?(c.success("操作成功"),m()):c.error(p.msg)})}).catch(e=>{console.error(e)})},z=r=>{x.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(e=>{b.delete("/productOrders/delete/"+r).then(p=>{p.code==="200"?(c.success("删除订单成功"),m()):c.error(p.msg)})}).catch(e=>{console.error(e)})},m=()=>{b.get("/productOrders/selectPage",{params:{pageNum:t.pageNum,pageSize:t.pageSize,orderNo:t.orderNo,status:t.status}}).then(r=>{var e,p;r.code==="200"?(t.ordersData=(e=r.data)==null?void 0:e.list,t.total=(p=r.data)==null?void 0:p.total):c.error(r.msg)})},O=()=>{b.put("/productOrders/update",t.form).then(r=>{r.code==="200"&&(c.success("操作成功"),t.formVisible=!1,m())})},S=(r,e)=>{t.form=JSON.parse(JSON.stringify(r)),t.form.status=e,O()};m();const B=()=>{t.orderNo=null,t.status=null,m()};return(r,e)=>{const p=I,y=q,P=R,_=F,s=K,w=L,D=G,T=M,v=X,U=W,N=H,$=Q,A=j;return i(),V("div",Y,[g("div",Z,"特产订单（"+f(t.total)+"）",1),g("div",ee,[l(p,{modelValue:t.orderNo,"onUpdate:modelValue":e[0]||(e[0]=o=>t.orderNo=o),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入订单编号查询"},null,8,["modelValue"]),l(P,{modelValue:t.status,"onUpdate:modelValue":e[1]||(e[1]=o=>t.status=o),placeholder:"请选择订单状态查询",style:{width:"240px","margin-right":"10px"}},{default:a(()=>[l(y,{label:"待支付",value:"待支付"}),l(y,{label:"待发货",value:"待发货"}),l(y,{label:"待签收",value:"待签收"}),l(y,{label:"已签收",value:"已签收"}),l(y,{label:"已取消",value:"已取消"})]),_:1},8,["modelValue"]),l(_,{type:"info",plain:"",onClick:m},{default:a(()=>e[6]||(e[6]=[n("查询")])),_:1,__:[6]}),l(_,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:B},{default:a(()=>e[7]||(e[7]=[n("重置")])),_:1,__:[7]})]),g("div",te,[l(D,{stripe:"",data:t.ordersData},{default:a(()=>[l(s,{prop:"orderNo",label:"订单号","show-overflow-tooltip":""}),l(s,{prop:"userName",label:"下单用户",width:"120"}),l(s,{prop:"name",label:"下单人"}),l(s,{prop:"phone",label:"联系电话","show-overflow-tooltip":""}),l(s,{prop:"address",label:"地址","show-overflow-tooltip":""}),l(s,{prop:"productName",label:"商品",width:"120"}),l(s,{prop:"num",label:"数量"}),l(s,{prop:"price",label:"总金额"},{default:a(o=>[g("span",oe,"￥"+f(o.row.price),1)]),_:1}),l(s,{prop:"time",label:"下单时间","show-overflow-tooltip":""}),l(s,{prop:"payTime",label:"支付时间","show-overflow-tooltip":""}),l(s,{prop:"payNo",label:"支付编号","show-overflow-tooltip":""}),l(s,{prop:"status",label:"订单状态"},{default:a(o=>[o.row.status==="待支付"?(i(),u(w,{key:0,type:"warning"},{default:a(()=>[n(f(o.row.status),1)]),_:2},1024)):d("",!0),o.row.status==="待发货"?(i(),u(w,{key:1,type:"info"},{default:a(()=>[n(f(o.row.status),1)]),_:2},1024)):d("",!0),o.row.status==="待签收"?(i(),u(w,{key:2,type:"primary"},{default:a(()=>[n(f(o.row.status),1)]),_:2},1024)):d("",!0),o.row.status==="已签收"?(i(),u(w,{key:3,type:"success"},{default:a(()=>[n(f(o.row.status),1)]),_:2},1024)):d("",!0),o.row.status==="已取消"?(i(),u(w,{key:4,type:"danger"},{default:a(()=>[n(f(o.row.status),1)]),_:2},1024)):d("",!0)]),_:1}),l(s,{label:"操作",width:"180",fixed:"right"},{default:a(o=>[o.row.status==="待支付"?(i(),u(_,{key:0,size:"small",type:"info",onClick:k=>C(o.row)},{default:a(()=>e[8]||(e[8]=[n("支付")])),_:2,__:[8]},1032,["onClick"])):d("",!0),o.row.status==="待签收"?(i(),u(_,{key:1,size:"small",type:"primary",onClick:k=>S(o.row,"已签收")},{default:a(()=>e[9]||(e[9]=[n("签收")])),_:2,__:[9]},1032,["onClick"])):d("",!0),o.row.status==="待支付"?(i(),u(_,{key:2,size:"small",type:"danger",onClick:k=>h(o.row)},{default:a(()=>e[10]||(e[10]=[n("取消")])),_:2,__:[10]},1032,["onClick"])):d("",!0),o.row.status==="已取消"||o.row.status==="已签收"?(i(),u(_,{key:3,size:"small",type:"danger",onClick:k=>z(o.row.id)},{default:a(()=>e[11]||(e[11]=[n("删除订单")])),_:2,__:[11]},1032,["onClick"])):d("",!0)]),_:1})]),_:1},8,["data"])]),t.total?(i(),V("div",le,[l(T,{onCurrentChange:m,background:"",layout:"prev, pager, next","page-size":t.pageSize,"current-page":t.pageNum,"onUpdate:currentPage":e[2]||(e[2]=o=>t.pageNum=o),total:t.total},null,8,["page-size","current-page","total"])])):d("",!0),l(A,{title:"支付订单",modelValue:t.formVisible,"onUpdate:modelValue":e[5]||(e[5]=o=>t.formVisible=o),width:"40%","destroy-on-close":""},{footer:a(()=>[g("span",re,[l(_,{onClick:e[4]||(e[4]=o=>t.formVisible=!1)},{default:a(()=>e[12]||(e[12]=[n("取 消")])),_:1,__:[12]}),l(_,{type:"primary",onClick:E},{default:a(()=>e[13]||(e[13]=[n("支 付")])),_:1,__:[13]})])]),default:a(()=>[l($,{ref:"form",model:t.form,"label-width":"70px",style:{padding:"20px"}},{default:a(()=>[l(N,{prop:"type",label:"支付方式"},{default:a(()=>[l(U,{modelValue:t.type,"onUpdate:modelValue":e[3]||(e[3]=o=>t.type=o),fill:"#A3A6AD"},{default:a(()=>[l(v,{label:"支付宝",value:"aliPay"}),l(v,{label:"微信",value:"wePay"})]),_:1},8,["modelValue"])]),_:1}),l(N,{prop:"price",label:"支付金额"},{default:a(()=>[g("span",ae,"￥"+f(t.form.price),1)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}};export{Ve as default};
