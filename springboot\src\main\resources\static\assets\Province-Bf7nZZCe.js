/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as p}from"./request-BNOh435t.js";/* empty css                    */import{O as I,r as J,c as g,a as u,n as O,b as a,z as T,w as l,A as $,B as q,C as A,D as F,o as _,j as m,G as M,u as b,I as R,J as j,K as G,L as K,N as y,E as d}from"./index-DXyN40AS.js";const L={class:"card",style:{"margin-bottom":"5px"}},H={class:"card",style:{"margin-bottom":"5px"}},Q={class:"card",style:{"margin-bottom":"5px"}},W={key:0,class:"card"},X={class:"dialog-footer"},fe={__name:"Province",setup(Y){const f=I(),e=J({formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,name:null,ids:[],rules:{name:[{required:!0,message:"请输入省份名称",trigger:"blur"}]}}),r=()=>{p.get("/province/selectPage",{params:{pageNum:e.pageNum,pageSize:e.pageSize,name:e.name}}).then(o=>{var t,i;o.code==="200"&&(e.tableData=((t=o.data)==null?void 0:t.list)||[],e.total=(i=o.data)==null?void 0:i.total)})},V=()=>{e.form={},e.formVisible=!0},v=o=>{e.form=JSON.parse(JSON.stringify(o)),e.formVisible=!0},C=()=>{p.post("/province/add",e.form).then(o=>{o.code==="200"?(d.success("操作成功"),e.formVisible=!1,r()):d.error(o.msg)})},x=()=>{p.put("/province/update",e.form).then(o=>{o.code==="200"&&(d.success("操作成功"),e.formVisible=!1,r())})},k=()=>{f.value.validate(o=>{o&&(e.form.id?x():C())})},h=o=>{y.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(t=>{p.delete("/province/delete/"+o).then(i=>{i.code==="200"?(d.success("删除成功"),r()):d.error(i.msg)})}).catch(t=>{console.error(t)})},w=()=>{if(!e.ids.length){d.warning("请选择数据");return}y.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(o=>{p.delete("/province/delete/batch",{data:e.ids}).then(t=>{t.code==="200"?(d.success("操作成功"),r()):d.error(t.msg)})}).catch(o=>{console.error(o)})},E=o=>{e.ids=o.map(t=>t.id)},N=()=>{e.name=null,r()};return r(),(o,t)=>{const i=T,s=$,c=M,S=q,z=A,B=K,D=G,P=F;return _(),g("div",null,[u("div",L,[a(i,{modelValue:e.name,"onUpdate:modelValue":t[0]||(t[0]=n=>e.name=n),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入省份名称查询"},null,8,["modelValue"]),a(s,{type:"info",plain:"",onClick:r},{default:l(()=>t[5]||(t[5]=[m("查询")])),_:1,__:[5]}),a(s,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:N},{default:l(()=>t[6]||(t[6]=[m("重置")])),_:1,__:[6]})]),u("div",H,[a(s,{type:"primary",plain:"",onClick:V},{default:l(()=>t[7]||(t[7]=[m("新增")])),_:1,__:[7]}),a(s,{type:"danger",plain:"",onClick:w},{default:l(()=>t[8]||(t[8]=[m("批量删除")])),_:1,__:[8]})]),u("div",Q,[a(S,{stripe:"",data:e.tableData,onSelectionChange:E},{default:l(()=>[a(c,{type:"selection",width:"55"}),a(c,{prop:"name",label:"省份名称"}),a(c,{label:"操作",width:"100",fixed:"right"},{default:l(n=>[a(s,{type:"primary",circle:"",icon:b(R),onClick:U=>v(n.row)},null,8,["icon","onClick"]),a(s,{type:"danger",circle:"",icon:b(j),onClick:U=>h(n.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),e.total?(_(),g("div",W,[a(z,{onCurrentChange:r,background:"",layout:"prev, pager, next","page-size":e.pageSize,"current-page":e.pageNum,"onUpdate:currentPage":t[1]||(t[1]=n=>e.pageNum=n),total:e.total},null,8,["page-size","current-page","total"])])):O("",!0),a(P,{name:"省份信息",modelValue:e.formVisible,"onUpdate:modelValue":t[4]||(t[4]=n=>e.formVisible=n),width:"40%","destroy-on-close":""},{footer:l(()=>[u("span",X,[a(s,{onClick:t[3]||(t[3]=n=>e.formVisible=!1)},{default:l(()=>t[9]||(t[9]=[m("取 消")])),_:1,__:[9]}),a(s,{type:"primary",onClick:k},{default:l(()=>t[10]||(t[10]=[m("确 定")])),_:1,__:[10]})])]),default:l(()=>[a(D,{ref_key:"formRef",ref:f,model:e.form,rules:e.rules,"label-width":"70px",style:{padding:"20px"}},{default:l(()=>[a(B,{prop:"name",label:"名称"},{default:l(()=>[a(i,{modelValue:e.form.name,"onUpdate:modelValue":t[2]||(t[2]=n=>e.form.name=n),placeholder:"请输入省份名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}};export{fe as default};
