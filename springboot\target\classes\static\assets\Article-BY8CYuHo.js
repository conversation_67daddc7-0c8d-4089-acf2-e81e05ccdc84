/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css              *//* empty css                 *//* empty css               *//* empty css              */import{r as h}from"./request-BNOh435t.js";/* empty css                    */import{T as B,E as O}from"./index.esm-tRL6DuoP.js";import{r as M,O as T,V as q,W as S,c as u,a as r,b as o,w as i,A as L,F as $,x as j,D as J,h as v,o as c,j as d,t as a,k as K,K as W,L as G,M as H,u as f,z as P,E as b}from"./index-DXyN40AS.js";import"./index.esm-CA4zuM-h.js";const Q={style:{width:"60%",margin:"20px auto","min-height":"600px"}},X={class:"card",style:{display:"flex"}},Y={style:{"margin-top":"10px",display:"flex","grid-gap":"20px"},class:"card"},Z=["src"],ee={style:{"font-size":"18px"}},te=["onClick"],oe={style:{"margin-top":"20px",display:"flex","align-items":"center"}},le=["src"],se={style:{"margin-left":"5px","margin-right":"20px",color:"#74726b"}},re={style:{"margin-left":"5px","margin-right":"20px",color:"#74726b"}},ie={style:{"margin-left":"5px","margin-right":"20px"}},ae={style:{border:"1px solid #ccc",width:"100%"}},ne={class:"dialog-footer"},V="default",we={__name:"Article",setup(de){const _="http://:9090",t=M({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),formVisible:!1,articleData:[],form:{},rules:{title:[{required:!0,message:"请输入帖子名称",trigger:"blur"}],content:[{required:!0,message:"请输入帖子详情",trigger:"blur"}]}}),g=T(),p=q(),x={MENU_CONF:{}};x.MENU_CONF.uploadImage={headers:{token:t.user.token},server:_+"/files/wang/upload",fieldName:"file"},S(()=>{const s=p.value;s!=null&&s.destroy()});const C=s=>{p.value=s},w=s=>{t.form.img=s.data},E=()=>{t.form={},t.formVisible=!0},k=()=>{g.value.validate(s=>{s&&h.post("/article/add",t.form).then(e=>{e.code==="200"?(b.success("发布成功，等待管理员审核"),t.formVisible=!1):b.error(e.msg)})})},N=()=>{h.get("/article/selectAll",{params:{status:"通过"}}).then(s=>{s.code==="200"&&(t.articleData=s.data)})},D=s=>{location.href=s};return N(),(s,e)=>{const n=L,U=v("View"),y=K,A=v("ChatDotRound"),F=H,m=G,I=P,R=W,z=J;return c(),u("div",Q,[r("div",X,[e[5]||(e[5]=r("div",{style:{"font-size":"20px",flex:"1"}},"旅游相关帖子",-1)),r("div",null,[o(n,{type:"success",onClick:E},{default:i(()=>e[4]||(e[4]=[d("发帖")])),_:1,__:[4]})])]),(c(!0),u($,null,j(t.articleData,l=>(c(),u("div",Y,[r("img",{src:l.img,alt:"",style:{width:"200px",height:"150px","border-radius":"5px"}},null,8,Z),r("div",null,[r("div",ee,a(l.title),1),r("div",{onClick:pe=>D("/front/articleDetail?id="+l.id),class:"line4",style:{color:"#74726b","margin-top":"10px",cursor:"pointer"}},a(l.content),9,te),r("div",oe,[r("img",{src:l.userAvatar,alt:"",style:{width:"20px",height:"20px","border-radius":"50%"}},null,8,le),r("div",se,a(l.userName),1),o(y,{size:"15"},{default:i(()=>[o(U)]),_:1}),r("div",re,a(l.views),1),o(y,{size:"15"},{default:i(()=>[o(A)]),_:1}),r("div",ie,a(l.comment),1)])])]))),256)),o(z,{title:"帖子信息",modelValue:t.formVisible,"onUpdate:modelValue":e[3]||(e[3]=l=>t.formVisible=l),width:"50%","destroy-on-close":""},{footer:i(()=>[r("span",ne,[o(n,{onClick:e[2]||(e[2]=l=>t.formVisible=!1)},{default:i(()=>e[7]||(e[7]=[d("取 消")])),_:1,__:[7]}),o(n,{type:"primary",onClick:k},{default:i(()=>e[8]||(e[8]=[d("确 定")])),_:1,__:[8]})])]),default:i(()=>[o(R,{ref_key:"formRef",ref:g,model:t.form,rules:t.rules,"label-width":"110px",style:{padding:"20px"}},{default:i(()=>[o(m,{prop:"img",label:"封面"},{default:i(()=>[o(F,{action:f(_)+"/files/upload","on-success":w,"list-type":"picture"},{default:i(()=>[o(n,{type:"primary"},{default:i(()=>e[6]||(e[6]=[d("上传封面")])),_:1,__:[6]})]),_:1},8,["action"])]),_:1}),o(m,{prop:"title",label:"帖子名称"},{default:i(()=>[o(I,{modelValue:t.form.title,"onUpdate:modelValue":e[0]||(e[0]=l=>t.form.title=l),placeholder:"请输入帖子名称"},null,8,["modelValue"])]),_:1}),o(m,{prop:"content",label:"游玩详细介绍"},{default:i(()=>[r("div",ae,[o(f(B),{style:{"border-bottom":"1px solid #ccc"},editor:p.value,mode:V},null,8,["editor"]),o(f(O),{style:{height:"500px","overflow-y":"hidden"},modelValue:t.form.content,"onUpdate:modelValue":e[1]||(e[1]=l=>t.form.content=l),mode:V,defaultConfig:x,onOnCreated:C},null,8,["modelValue"])])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}};export{we as default};
