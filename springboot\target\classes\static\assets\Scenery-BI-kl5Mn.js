/* empty css                *//* empty css            *//* empty css             *//* empty css               *//* empty css            *//* empty css               *//* empty css                  *//* empty css              */import{r as g}from"./request-BNOh435t.js";import{r as N,c,a as t,b as n,z as U,w as i,Q as z,A as L,a0 as R,E as v,h as p,o as s,F as x,x as f,m as y,R as F,j as h,a2 as P,k as T,t as m,X as j}from"./index-DXyN40AS.js";const q={style:{width:"60%",margin:"20px auto","min-height":"600px"}},M={style:{"margin-top":"20px"}},Q={class:"card",style:{display:"flex","grid-gap":"10px"}},X=["src","onClick"],$={style:{color:"#74726b"}},G={style:{"font-size":"18px",display:"flex","align-items":"center","grid-gap":"10px"}},H={style:{display:"flex","align-items":"center","grid-gap":"5px","margin-top":"10px"}},J={style:{display:"flex","align-items":"center","grid-gap":"5px","margin-top":"10px"}},K={style:{display:"flex","align-items":"center","grid-gap":"5px","margin-top":"10px"}},pe={__name:"Scenery",setup(W){const o=N({name:null,provinceId:null,provinceData:null,sceneryData:[]}),d=()=>{g.get("/scenery/selectAll",{params:{name:o.name,provinceId:o.provinceId}}).then(l=>{l.code==="200"?o.sceneryData=l.data:v.error(l.msg)})};d(),(()=>{g.get("province/selectAll").then(l=>{l.code==="200"?o.provinceData=l.data:v.error(l.msg)})})();const V=()=>{o.name=null,o.provinceId=null,d()},E=l=>{location.href=l};return(l,a)=>{const w=U,I=F,k=z,u=L,b=p("OfficeBuilding"),r=T,D=p("LocationInformation"),B=p("Apple"),C=p("Opportunity"),A=j,O=P,S=R;return s(),c("div",q,[t("div",null,[n(w,{modelValue:o.name,"onUpdate:modelValue":a[0]||(a[0]=e=>o.name=e),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入景点名称查询"},null,8,["modelValue"]),n(k,{modelValue:o.provinceId,"onUpdate:modelValue":a[1]||(a[1]=e=>o.provinceId=e),placeholder:"请选择省份",style:{width:"240px",margin:"0 10px"}},{default:i(()=>[(s(!0),c(x,null,f(o.provinceData,e=>(s(),y(I,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),n(u,{type:"info",plain:"",onClick:d},{default:i(()=>a[2]||(a[2]=[h("查询")])),_:1,__:[2]}),n(u,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:V},{default:i(()=>a[3]||(a[3]=[h("重置")])),_:1,__:[3]})]),t("div",M,[n(S,{gutter:"10"},{default:i(()=>[(s(!0),c(x,null,f(o.sceneryData,e=>(s(),y(O,{span:12,style:{"margin-bottom":"10px"}},{default:i(()=>[t("div",Q,[t("img",{src:e.img,alt:"“”",style:{width:"200px",height:"120px","border-radius":"5px",cursor:"pointer"},onClick:_=>E("/front/sceneryDetail?id="+e.id)},null,8,X),t("div",$,[t("div",G,[n(r,{size:"20"},{default:i(()=>[n(b)]),_:1}),t("div",null,"景点名称："+m(e.name),1)]),t("div",H,[n(r,null,{default:i(()=>[n(D)]),_:1}),t("div",null,"所属省份："+m(e.provinceName),1)]),t("div",J,[n(r,null,{default:i(()=>[n(B)]),_:1}),t("div",null,"浏览量："+m(e.views),1)]),t("div",K,[n(r,null,{default:i(()=>[n(C)]),_:1}),a[4]||(a[4]=t("div",null,"推荐指数： ",-1)),n(A,{modelValue:e.recommend,"onUpdate:modelValue":_=>e.recommend=_,disabled:""},null,8,["modelValue","onUpdate:modelValue"])])])])]),_:2},1024))),256))]),_:1})])])}}};export{pe as default};
