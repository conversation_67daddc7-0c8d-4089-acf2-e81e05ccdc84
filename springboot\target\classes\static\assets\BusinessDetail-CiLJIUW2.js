/* empty css                *//* empty css            */import{r as p}from"./request-BNOh435t.js";import{r as y,f,c,a as t,t as o,j as a,b as l,w as d,a0 as b,E as _,h as v,o as r,F as D,x as w,m as k,a2 as B,k as z}from"./index-DXyN40AS.js";const E={style:{width:"80%",margin:"20px auto","min-height":"600px"}},I={class:"card",style:{display:"flex","grid-gap":"20px"}},C=["src"],F={style:{flex:"1",padding:"15px"}},T={style:{"font-size":"24px","font-weight":"bold"}},N={style:{"font-size":"15px","margin-top":"13px"}},V=["href"],j={style:{"margin-top":"9px"}},q={style:{"margin-top":"9px"}},H={style:{"margin-top":"9px"}},R={style:{"font-weight":"bold","font-size":"20px",color:"red"}},A={style:{"margin-top":"9px"}},L={style:{"margin-top":"9px","line-height":"25px","text-align":"justify"},class:"line4"},M={style:{"margin-top":"20px"}},S={class:"front_card"},$=["onClick","src"],G={style:{padding:"20px"}},J={style:{"font-size":"16px","font-weight":"bold"}},K={style:{display:"flex","align-items":"center","margin-top":"15px"}},O={style:{flex:"1","margin-right":"5px"}},P={style:{width:"80px","font-weight":"bold","font-size":"20px",color:"red","text-align":"right"}},et={__name:"BusinessDetail",setup(Q){const s=y({businessData:{},typeData:[],businessId:f.currentRoute.value.query.id});(()=>{p.get("/business/selectById/"+s.businessId).then(e=>{e.code==="200"?s.businessData=e.data:_.error(e.msg)})})(),(()=>{p.get("/type/selectAll",{params:{businessId:s.businessId}}).then(e=>{e.code==="200"?s.typeData=e.data:_.error(e.msg)})})();const u=e=>{location.href=e};return(e,i)=>{const h=v("HomeFilled"),g=z,x=B,m=b;return r(),c("div",E,[t("div",I,[t("img",{src:s.businessData.img,alt:"",style:{width:"500px",height:"300px","border-radius":"5px"}},null,8,C),t("div",F,[t("div",T,o(s.businessData.name),1),t("div",N,[t("div",null,[i[0]||(i[0]=a("宾馆网站：")),t("a",{href:s.businessData.link,target:"_blank"},o(s.businessData.link),9,V)]),t("div",j,"联系电话："+o(s.businessData.phone),1),t("div",q,"官方邮箱："+o(s.businessData.email),1),t("div",H,[i[1]||(i[1]=a("入住价格：")),t("span",R,"￥"+o(s.businessData.price),1),i[2]||(i[2]=a(" 起"))]),t("div",A,"宾馆地址："+o(s.businessData.address),1),t("div",L,"详细介绍："+o(s.businessData.content),1)])])]),t("div",M,[l(m,{gutter:20},{default:d(()=>[(r(!0),c(D,null,w(s.typeData,n=>(r(),k(x,{span:6},{default:d(()=>[t("div",S,[t("img",{onClick:X=>u("/front/typeDetail?id="+n.id),src:n.img,alt:"",style:{width:"100%",height:"200px","border-radius":"5px 5px 0 0",cursor:"pointer"}},null,8,$),t("div",G,[t("div",J,o(n.name),1),t("div",K,[l(g,{size:"18"},{default:d(()=>[l(h)]),_:1}),t("div",O,"剩余 "+o(n.num)+" 间",1),t("div",P,"￥"+o(n.price),1)])])])]),_:2},1024))),256))]),_:1})])])}}};export{et as default};
