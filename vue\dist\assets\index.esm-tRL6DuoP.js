import{i as C,Q as y,a as E}from"./index.esm-CA4zuM-h.js";import{a3 as g,c as h,o as w,O as d,V as O,a4 as P,a5 as $,a6 as b,a7 as F}from"./index-DXyN40AS.js";var B=Object.defineProperty,D=Object.defineProperties,j=Object.getOwnPropertyDescriptors,m=Object.getOwnPropertySymbols,H=Object.prototype.hasOwnProperty,S=Object.prototype.propertyIsEnumerable,_=(e,t,o)=>t in e?B(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,V=(e,t)=>{for(var o in t||(t={}))H.call(t,o)&&_(e,o,t[o]);if(m)for(var o of m(t))S.call(t,o)&&_(e,o,t[o]);return e},A=(e,t)=>D(e,j(t));function u(e){let t=`请使用 '@${e}' 事件，不要放在 props 中`;return t+=`
Please use '@${e}' event instead of props`,t}var v=(e,t)=>{for(const[o,a]of t)e[o]=a;return e};const M=g({props:{mode:{type:String,default:"default"},defaultContent:{type:Array,default:[]},defaultHtml:{type:String,default:""},defaultConfig:{type:Object,default:{}},modelValue:{type:String,default:""}},setup(e,t){const o=d(null),a=O(null),i=d(""),s=()=>{if(!o.value)return;const l=b(e.defaultContent);C({selector:o.value,mode:e.mode,content:l||[],html:e.defaultHtml||e.modelValue||"",config:A(V({},e.defaultConfig),{onCreated(r){if(a.value=r,t.emit("onCreated",r),e.defaultConfig.onCreated){const n=u("onCreated");throw new Error(n)}},onChange(r){const n=r.getHtml();if(i.value=n,t.emit("update:modelValue",n),t.emit("onChange",r),e.defaultConfig.onChange){const f=u("onChange");throw new Error(f)}},onDestroyed(r){if(t.emit("onDestroyed",r),e.defaultConfig.onDestroyed){const n=u("onDestroyed");throw new Error(n)}},onMaxLength(r){if(t.emit("onMaxLength",r),e.defaultConfig.onMaxLength){const n=u("onMaxLength");throw new Error(n)}},onFocus(r){if(t.emit("onFocus",r),e.defaultConfig.onFocus){const n=u("onFocus");throw new Error(n)}},onBlur(r){if(t.emit("onBlur",r),e.defaultConfig.onBlur){const n=u("onBlur");throw new Error(n)}},customAlert(r,n){if(t.emit("customAlert",r,n),e.defaultConfig.customAlert){const f=u("customAlert");throw new Error(f)}},customPaste:(r,n)=>{if(e.defaultConfig.customPaste){const c=u("customPaste");throw new Error(c)}let f;return t.emit("customPaste",r,n,c=>{f=c}),f}})})};function p(l){const r=a.value;r!=null&&r.setHtml(l)}return P(()=>{s()}),$(()=>e.modelValue,l=>{l!==i.value&&p(l)}),{box:o}}}),L={ref:"box",style:{height:"100%"}};function T(e,t,o,a,i,s){return w(),h("div",L,null,512)}var N=v(M,[["render",T]]);const I=g({props:{editor:{type:Object},mode:{type:String,default:"default"},defaultConfig:{type:Object,default:{}}},setup(e){const t=d(null),o=a=>{if(t.value){if(a==null)throw new Error("Not found instance of Editor when create <Toolbar/> component");y.getToolbar(a)||E({editor:a,selector:t.value||"<div></div>",mode:e.mode,config:e.defaultConfig})}};return F(()=>{const{editor:a}=e;a!=null&&o(a)}),{selector:t}}}),R={ref:"selector"};function x(e,t,o,a,i,s){return w(),h("div",R,null,512)}var q=v(I,[["render",x]]);export{N as E,q as T};
