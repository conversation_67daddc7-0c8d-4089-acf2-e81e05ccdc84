/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                     *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css             *//* empty css               */import{r as V}from"./request-BNOh435t.js";/* empty css                    */import{r as J,O as q,c as O,a as y,n as m,b as l,t as g,z as F,w as r,Q as G,A as M,B as j,C as K,D as L,E as p,o as d,R as Q,j as s,G as X,m as f,P as H,X as W,K as Y,L as Z,T as ee,U as te,N as z}from"./index-DXyN40AS.js";const oe={style:{width:"80%",margin:"20px auto","min-height":"600px"}},le={style:{"font-size":"18px"}},re={class:"card",style:{margin:"20px 0"}},ae={class:"card",style:{"margin-bottom":"5px"}},se={style:{color:"red"}},ne={key:0,class:"card"},ie={style:{color:"red"}},de={class:"dialog-footer"},ue={class:"dialog-footer"},Se={__name:"BusinessOrders",setup(me){const o=J({orderNo:null,status:null,total:0,pageNum:1,pageSize:5,ordersData:[],form:{},type:"aliPay",formVisible:!1,formVisible2:!1,rules:{score:[{required:!0,message:"请给出评分",trigger:"blur"}],comment:[{required:!0,message:"请输入评价内容",trigger:"blur"}]}}),v=q(),S=a=>{o.form=JSON.parse(JSON.stringify(a)),o.formVisible=!0},U=()=>{V.put("/businessOrders/pay",o.form).then(a=>{a.code==="200"?(p.success("下单成功"),o.formVisible=!1,_()):p.error(a.msg)})},B=a=>{o.form=JSON.parse(JSON.stringify(a)),o.formVisible2=!0},h=()=>{v.value.validate(a=>{a&&V.put("/businessOrders/comment",o.form).then(e=>{e.code==="200"?(p.success("评分成功"),o.formVisible2=!1,_()):p.error(e.msg)})})},D=a=>{z.confirm("取消后订单无法恢复，您确定取消吗？","取消订单确认",{type:"warning"}).then(e=>{V.put("/businessOrders/cancel",a).then(i=>{i.code==="200"?(p.success("操作成功"),_()):p.error(i.msg)})}).catch(e=>{console.error(e)})},P=a=>{z.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(e=>{V.delete("/businessOrders/delete/"+a).then(i=>{i.code==="200"?(p.success("删除订单成功"),_()):p.error(i.msg)})}).catch(e=>{console.error(e)})},_=()=>{V.get("/businessOrders/selectPage",{params:{pageNum:o.pageNum,pageSize:o.pageSize,orderNo:o.orderNo,status:o.status}}).then(a=>{var e,i;a.code==="200"?(o.ordersData=(e=a.data)==null?void 0:e.list,o.total=(i=a.data)==null?void 0:i.total):p.error(a.msg)})};_();const R=()=>{o.orderNo=null,o.status=null,_()};return(a,e)=>{const i=F,b=Q,T=G,u=M,n=X,w=H,N=W,$=j,A=K,x=te,I=ee,k=Z,C=Y,E=L;return d(),O("div",oe,[y("div",le,"宾馆订单（"+g(o.total)+"）",1),y("div",re,[l(i,{modelValue:o.orderNo,"onUpdate:modelValue":e[0]||(e[0]=t=>o.orderNo=t),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入订单编号查询"},null,8,["modelValue"]),l(T,{modelValue:o.status,"onUpdate:modelValue":e[1]||(e[1]=t=>o.status=t),placeholder:"请选择订单状态查询",style:{width:"240px","margin-right":"10px"}},{default:r(()=>[l(b,{label:"待支付",value:"待支付"}),l(b,{label:"待入住",value:"待入住"}),l(b,{label:"已入住",value:"已入住"}),l(b,{label:"已取消",value:"已取消"}),l(b,{label:"已退房",value:"已退房"})]),_:1},8,["modelValue"]),l(u,{type:"info",plain:"",onClick:_},{default:r(()=>e[10]||(e[10]=[s("查询")])),_:1,__:[10]}),l(u,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:R},{default:r(()=>e[11]||(e[11]=[s("重置")])),_:1,__:[11]})]),y("div",ae,[l($,{stripe:"",data:o.ordersData},{default:r(()=>[l(n,{prop:"orderNo",label:"订单号","show-overflow-tooltip":""}),l(n,{prop:"userName",label:"下单用户",width:"120"}),l(n,{prop:"businessName",label:"宾馆名称",width:"120"}),l(n,{prop:"typeName",label:"房间类型",width:"120"}),l(n,{prop:"start",label:"入住时间","show-overflow-tooltip":""}),l(n,{prop:"end",label:"离开时间","show-overflow-tooltip":""}),l(n,{prop:"price",label:"总金额"},{default:r(t=>[y("span",se,"￥"+g(t.row.price),1)]),_:1}),l(n,{prop:"payTime",label:"支付时间","show-overflow-tooltip":""}),l(n,{prop:"payNo",label:"支付编号","show-overflow-tooltip":""}),l(n,{prop:"comment",label:"评论","show-overflow-tooltip":""}),l(n,{prop:"status",label:"订单状态"},{default:r(t=>[t.row.status==="待支付"?(d(),f(w,{key:0,type:"warning"},{default:r(()=>[s(g(t.row.status),1)]),_:2},1024)):m("",!0),t.row.status==="待入住"?(d(),f(w,{key:1,type:"info"},{default:r(()=>[s(g(t.row.status),1)]),_:2},1024)):m("",!0),t.row.status==="已退房"?(d(),f(w,{key:2,type:"primary"},{default:r(()=>[s(g(t.row.status),1)]),_:2},1024)):m("",!0),t.row.status==="已入住"?(d(),f(w,{key:3,type:"success"},{default:r(()=>[s(g(t.row.status),1)]),_:2},1024)):m("",!0),t.row.status==="已取消"?(d(),f(w,{key:4,type:"danger"},{default:r(()=>[s(g(t.row.status),1)]),_:2},1024)):m("",!0)]),_:1}),l(n,{prop:"score",label:"评分",width:"150"},{default:r(t=>[l(N,{modelValue:t.row.score,"onUpdate:modelValue":c=>t.row.score=c,disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(n,{label:"操作",width:"180",fixed:"right"},{default:r(t=>[t.row.status==="待支付"?(d(),f(u,{key:0,size:"small",type:"info",onClick:c=>S(t.row)},{default:r(()=>e[12]||(e[12]=[s("支付")])),_:2,__:[12]},1032,["onClick"])):m("",!0),t.row.status==="待支付"?(d(),f(u,{key:1,size:"small",type:"danger",onClick:c=>D(t.row)},{default:r(()=>e[13]||(e[13]=[s("取消")])),_:2,__:[13]},1032,["onClick"])):m("",!0),t.row.status==="已退房"&&!t.row.comment?(d(),f(u,{key:2,size:"small",type:"primary",onClick:c=>B(t.row)},{default:r(()=>e[14]||(e[14]=[s("评价")])),_:2,__:[14]},1032,["onClick"])):m("",!0),t.row.status==="已取消"||t.row.status==="已退房"?(d(),f(u,{key:3,size:"small",type:"danger",onClick:c=>P(t.row.id)},{default:r(()=>e[15]||(e[15]=[s("删除订单")])),_:2,__:[15]},1032,["onClick"])):m("",!0)]),_:1})]),_:1},8,["data"])]),o.total?(d(),O("div",ne,[l(A,{onCurrentChange:_,background:"",layout:"prev, pager, next","page-size":o.pageSize,"current-page":o.pageNum,"onUpdate:currentPage":e[2]||(e[2]=t=>o.pageNum=t),total:o.total},null,8,["page-size","current-page","total"])])):m("",!0),l(E,{title:"支付订单",modelValue:o.formVisible,"onUpdate:modelValue":e[5]||(e[5]=t=>o.formVisible=t),width:"40%","destroy-on-close":""},{footer:r(()=>[y("span",de,[l(u,{onClick:e[4]||(e[4]=t=>o.formVisible=!1)},{default:r(()=>e[16]||(e[16]=[s("取 消")])),_:1,__:[16]}),l(u,{type:"primary",onClick:U},{default:r(()=>e[17]||(e[17]=[s("支 付")])),_:1,__:[17]})])]),default:r(()=>[l(C,{ref:"form",model:o.form,"label-width":"70px",style:{padding:"20px"}},{default:r(()=>[l(k,{prop:"comment",label:"支付方式"},{default:r(()=>[l(I,{modelValue:o.type,"onUpdate:modelValue":e[3]||(e[3]=t=>o.type=t),fill:"#A3A6AD"},{default:r(()=>[l(x,{label:"支付宝",value:"aliPay"}),l(x,{label:"微信",value:"wePay"})]),_:1},8,["modelValue"])]),_:1}),l(k,{prop:"price",label:"支付金额"},{default:r(()=>[y("span",ie,"￥"+g(o.form.price),1)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(E,{title:"评价信息",modelValue:o.formVisible2,"onUpdate:modelValue":e[9]||(e[9]=t=>o.formVisible2=t),width:"40%","destroy-on-close":""},{footer:r(()=>[y("span",ue,[l(u,{onClick:e[8]||(e[8]=t=>o.formVisible=!1)},{default:r(()=>e[18]||(e[18]=[s("取 消")])),_:1,__:[18]}),l(u,{type:"primary",onClick:h},{default:r(()=>e[19]||(e[19]=[s("提 交")])),_:1,__:[19]})])]),default:r(()=>[l(C,{ref_key:"formRef",ref:v,rules:o.rules,model:o.form,"label-width":"70px",style:{padding:"20px"}},{default:r(()=>[l(k,{prop:"comment",label:"评论"},{default:r(()=>[l(i,{modelValue:o.form.comment,"onUpdate:modelValue":e[6]||(e[6]=t=>o.form.comment=t),type:"textarea",rows:4},null,8,["modelValue"])]),_:1}),l(k,{prop:"score",label:"评分"},{default:r(()=>[l(N,{modelValue:o.form.score,"onUpdate:modelValue":e[7]||(e[7]=t=>o.form.score=t)},null,8,["modelValue"])]),_:1})]),_:1},8,["rules","model"])]),_:1},8,["modelValue"])])}}};export{Se as default};
