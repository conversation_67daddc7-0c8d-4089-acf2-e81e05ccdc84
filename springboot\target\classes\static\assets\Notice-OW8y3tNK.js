/* empty css                *//* empty css                      */import{r as p}from"./request-BNOh435t.js";import{r as d,c as s,a,b as _,w as l,v as u,E as f,o as n,F as g,x as h,m as x,y as D,t as c}from"./index-DXyN40AS.js";const v={style:{width:"50%",margin:"20px auto","min-height":"600px"}},w={class:"card"},F={__name:"Notice",setup(y){const t=d({noticeData:[]});return(()=>{p.get("/notice/selectAll").then(e=>{e.code==="200"?(t.noticeData=e.data,t.noticeData.length>3&&(t.noticeData=t.noticeData.slice(0,3))):f.error(e.msg)})})(),(e,i)=>{const r=D,m=u;return n(),s("div",v,[a("div",w,[i[0]||(i[0]=a("div",{style:{padding:"20px 30px","font-size":"18px","font-weight":"bold"}},"系统公告",-1)),_(m,{style:{"max-width":"600px"}},{default:l(()=>[(n(!0),s(g,null,h(t.noticeData,o=>(n(),x(r,{center:e.center,timestamp:o.time,placement:"top",color:"#0bbd87"},{default:l(()=>[a("h4",null,c(o.title),1),a("p",null,c(o.content),1)]),_:2},1032,["center","timestamp"]))),256))]),_:1})])])}}};export{F as default};
