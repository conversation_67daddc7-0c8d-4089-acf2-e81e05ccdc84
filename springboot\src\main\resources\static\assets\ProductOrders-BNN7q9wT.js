/* empty css                *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as w}from"./request-BNOh435t.js";/* empty css                    *//* empty css                */import{r as z,c as N,a as b,n as u,b as t,z as B,w as l,Q as O,A as P,B as T,C as D,o as n,R as U,j as p,G as J,t as c,m as d,P as M,N as $,E as y}from"./index-DXyN40AS.js";const j={class:"card",style:{"margin-bottom":"5px"}},q={class:"card",style:{"margin-bottom":"5px"}},A={style:{color:"red"}},G={key:0,class:"card"},at={__name:"ProductOrders",setup(I){const o=z({form:{},tableData:[],pageNum:1,pageSize:6,total:0,orderNo:null,status:null}),m=()=>{w.get("/productOrders/selectPage",{params:{pageNum:o.pageNum,pageSize:o.pageSize,orderNo:o.orderNo,status:o.status}}).then(s=>{var a,i;s.code==="200"&&(o.tableData=((a=s.data)==null?void 0:a.list)||[],o.total=(i=s.data)==null?void 0:i.total)})},v=()=>{w.put("/productOrders/update",o.form).then(s=>{s.code==="200"&&(y.success("操作成功"),o.formVisible=!1,m())})},k=s=>{$.confirm("取消后订单无法恢复，您确定取消吗？","取消订单确认",{type:"warning"}).then(a=>{w.put("/productOrders/cancel",s).then(i=>{i.code==="200"?(y.success("操作成功"),m()):y.error(i.msg)})}).catch(a=>{console.error(a)})},h=(s,a)=>{o.form=JSON.parse(JSON.stringify(s)),o.form.status=a,v()},x=()=>{o.orderNo=null,o.status=null,m()};return m(),(s,a)=>{const i=B,_=U,C=O,f=P,r=J,g=M,E=T,V=D;return n(),N("div",null,[b("div",j,[t(i,{modelValue:o.orderNo,"onUpdate:modelValue":a[0]||(a[0]=e=>o.orderNo=e),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入订单编号查询"},null,8,["modelValue"]),t(C,{modelValue:o.status,"onUpdate:modelValue":a[1]||(a[1]=e=>o.status=e),placeholder:"请选择订单状态查询",style:{width:"240px","margin-right":"10px"}},{default:l(()=>[t(_,{label:"待支付",value:"待支付"}),t(_,{label:"待发货",value:"待发货"}),t(_,{label:"待签收",value:"待签收"}),t(_,{label:"已签收",value:"已签收"}),t(_,{label:"已取消",value:"已取消"})]),_:1},8,["modelValue"]),t(f,{type:"info",plain:"",onClick:m},{default:l(()=>a[3]||(a[3]=[p("查询")])),_:1,__:[3]}),t(f,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:x},{default:l(()=>a[4]||(a[4]=[p("重置")])),_:1,__:[4]})]),b("div",q,[t(E,{stripe:"",data:o.tableData},{default:l(()=>[t(r,{prop:"orderNo",label:"订单号","show-overflow-tooltip":""}),t(r,{prop:"userName",label:"下单用户",width:"120"}),t(r,{prop:"name",label:"下单人"}),t(r,{prop:"phone",label:"联系电话","show-overflow-tooltip":""}),t(r,{prop:"address",label:"地址","show-overflow-tooltip":""}),t(r,{prop:"productName",label:"商品",width:"120"}),t(r,{prop:"num",label:"数量"}),t(r,{prop:"price",label:"总金额"},{default:l(e=>[b("span",A,"￥"+c(e.row.price),1)]),_:1}),t(r,{prop:"time",label:"下单时间","show-overflow-tooltip":""}),t(r,{prop:"payTime",label:"支付时间","show-overflow-tooltip":""}),t(r,{prop:"payNo",label:"支付编号","show-overflow-tooltip":""}),t(r,{prop:"status",label:"订单状态"},{default:l(e=>[e.row.status==="待支付"?(n(),d(g,{key:0,type:"warning"},{default:l(()=>[p(c(e.row.status),1)]),_:2},1024)):u("",!0),e.row.status==="待发货"?(n(),d(g,{key:1,type:"info"},{default:l(()=>[p(c(e.row.status),1)]),_:2},1024)):u("",!0),e.row.status==="待签收"?(n(),d(g,{key:2,type:"primary"},{default:l(()=>[p(c(e.row.status),1)]),_:2},1024)):u("",!0),e.row.status==="已签收"?(n(),d(g,{key:3,type:"success"},{default:l(()=>[p(c(e.row.status),1)]),_:2},1024)):u("",!0),e.row.status==="已取消"?(n(),d(g,{key:4,type:"danger"},{default:l(()=>[p(c(e.row.status),1)]),_:2},1024)):u("",!0)]),_:1}),t(r,{label:"操作",width:"100",fixed:"right"},{default:l(e=>[e.row.status==="待支付"?(n(),d(f,{key:0,type:"danger",onClick:S=>k(e.row)},{default:l(()=>a[5]||(a[5]=[p("取消")])),_:2,__:[5]},1032,["onClick"])):u("",!0),e.row.status==="待发货"?(n(),d(f,{key:1,type:"info",onClick:S=>h(e.row,"待签收")},{default:l(()=>a[6]||(a[6]=[p("发货")])),_:2,__:[6]},1032,["onClick"])):u("",!0)]),_:1})]),_:1},8,["data"])]),o.total?(n(),N("div",G,[t(V,{onCurrentChange:m,background:"",layout:"prev, pager, next","page-size":o.pageSize,"current-page":o.pageNum,"onUpdate:currentPage":a[2]||(a[2]=e=>o.pageNum=e),total:o.total},null,8,["page-size","current-page","total"])])):u("",!0)])}}};export{at as default};
