import{_ as x,i as p}from"./index-DABEre8E.js";import{r as l}from"./request-BNOh435t.js";/* empty css                */import{r as g,c as y,a as t,t as n,F as f,E as o,o as u}from"./index-DXyN40AS.js";const c="/assets/%E7%B1%BB%E5%9E%8B-BtodbzYJ.jpg",v="/assets/%E7%A9%BA%E9%97%B2-Cxc76-Uw.webp",h="/assets/%E4%BD%BF%E7%94%A8-CsxStgui.webp",b={style:{display:"flex","grid-gap":"10px"}},B={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},_={style:{flex:"1","font-size":"20px"}},D={style:{"font-weight":"bold",color:"red"}},E={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},w={style:{flex:"1","font-size":"20px"}},A={style:{"font-weight":"bold"}},L={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},S={style:{flex:"1","font-size":"20px"}},O={style:{"font-weight":"bold"}},z={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},C={style:{flex:"1","font-size":"20px"}},F={style:{"font-weight":"bold"}},H={__name:"HomeBusiness",setup(M){const i=g({baseData:{}});(()=>{l.get("/statistics/baseBusiness").then(s=>{s.code==="200"?i.baseData=s.data:o.error(s.msg)})})(),(()=>{l.get("/statistics/lineBusiness").then(s=>{if(s.code==="200"){let e=document.getElementById("line"),a=p(e);d.xAxis.data=s.data.xList,d.series[0].data=s.data.yList,a.setOption(d)}else o.error(s.msg)})})(),(()=>{l.get("/statistics/pieBusiness").then(s=>{if(s.code==="200"){let e=document.getElementById("pie"),a=p(e);m.series[0].data=s.data,a.setOption(m)}else o.error(s.msg)})})(),(()=>{l.get("/statistics/barBusiness").then(s=>{if(s.code==="200"){let e=document.getElementById("bar"),a=p(e);r.xAxis.data=s.data.xList,r.series[0].data=s.data.yList,a.setOption(r)}else o.error(s.msg)})})();let d={title:{text:"近一周每日此宾馆流水折线图",subtext:"统计维度：最近一周",left:"center"},legend:{data:[],template:""},grid:{left:"3%",right:"4%",bottom:"3%",top:"20%",containLabel:!0},tooltip:{trigger:"item"},xAxis:{name:"日期",type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]},yAxis:{name:"流水金额",type:"value"},series:[{name:"流水金额",data:[820,932,901,934,1290,1330,1320],type:"line",smooth:!0,markLine:{data:[{type:"average",name:"最近7天宾馆预订流水金额平均值"}]},markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]}}]},m={title:{text:"不同房间类型数量分布饼状图",subtext:"统计维度：房间类型",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"数量占比",type:"pie",radius:"50%",center:["50%","60%"],data:[{value:1048,name:"瑞幸咖啡"},{value:735,name:"雀巢咖啡"},{value:580,name:"星巴克咖啡"},{value:484,name:"栖巢咖啡"},{value:300,name:"小武哥咖啡"}]}]},r={title:{text:"不同房间类型空闲房间数量柱状图",subtext:"统计维度：房间类型",left:"center"},grid:{bottom:"10%",top:"25%"},legend:{orient:"vertical",left:"left"},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],name:"房间类型",axisLabel:{show:!0,interval:0,rotate:-60,inside:!1,margin:6}},yAxis:{type:"value",name:"空闲数量"},tooltip:{trigger:"item"},series:[{data:[120,200,150,80,70,110,130],type:"bar",itemStyle:{normal:{color:function(){return"#"+Math.floor(Math.random()*(256*256*256-1)).toString(16)}}}}]};return(s,e)=>(u(),y(f,null,[t("div",null,[t("div",b,[t("div",B,[e[1]||(e[1]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:x,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",_,[e[0]||(e[0]=t("div",null,"宾馆总流水",-1)),t("div",D,"￥"+n(i.baseData.businessTotal),1)])]),t("div",E,[e[3]||(e[3]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:c,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",w,[e[2]||(e[2]=t("div",null,"房间类型",-1)),t("div",A,n(i.baseData.typeNum),1)])]),t("div",L,[e[5]||(e[5]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:v,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",S,[e[4]||(e[4]=t("div",null,"空闲房间",-1)),t("div",O,n(i.baseData.emptyNum),1)])]),t("div",z,[e[7]||(e[7]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:h,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",C,[e[6]||(e[6]=t("div",null,"使用中房间",-1)),t("div",F,n(i.baseData.usingNum),1)])])]),e[8]||(e[8]=t("div",{style:{"margin-top":"10px",height:"500px"},class:"card",id:"line"},null,-1))]),e[9]||(e[9]=t("div",{style:{"margin-top":"10px",display:"flex","grid-gap":"10px"}},[t("div",{style:{flex:"2",height:"400px"},class:"card",id:"pie"}),t("div",{style:{flex:"3",height:"400px"},class:"card",id:"bar"})],-1))],64))}};export{H as default};
