/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                 *//* empty css              *//* empty css                     *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                *//* empty css                     *//* empty css               *//* empty css              */import{r as _}from"./request-BNOh435t.js";/* empty css                    */import{O as K,r as W,V as Q,W as X,c as C,a as b,n as g,b as o,z as Y,w as a,A as Z,B as ee,C as te,D as oe,o as f,j as s,G as le,m as w,H as re,u as c,I as ae,J as ie,K as ne,L as se,S as de,M as pe,N,E as m}from"./index-DXyN40AS.js";import{T as me,E as ue}from"./index.esm-tRL6DuoP.js";import"./index.esm-CA4zuM-h.js";const fe={class:"card",style:{"margin-bottom":"5px"}},ce={key:0,class:"card",style:{"margin-bottom":"5px"}},ge={class:"card",style:{"margin-bottom":"5px"}},_e={key:1,class:"card"},be={style:{border:"1px solid #ccc",width:"100%"}},ye={class:"dialog-footer"},we=["innerHTML"],S="default",He={__name:"Type",setup(Ve){const y="http://:9090",U=K(),e=W({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,name:null,ids:[],viewContent:null,viewVisible:null,rules:{name:[{required:!0,message:"请输入房间类型",trigger:"blur"}],description:[{required:!0,message:"请输入房间类型简介",trigger:"blur"}]}}),V=Q(),k={MENU_CONF:{}};k.MENU_CONF.uploadImage={headers:{token:e.user.token},server:y+"/files/wang/upload",fieldName:"file"},X(()=>{const r=V.value;r!=null&&r.destroy()});const I=r=>{V.value=r},p=()=>{_.get("/type/selectPage",{params:{pageNum:e.pageNum,pageSize:e.pageSize,name:e.name}}).then(r=>{var t,d;r.code==="200"&&(e.tableData=((t=r.data)==null?void 0:t.list)||[],e.total=(d=r.data)==null?void 0:d.total)})},B=()=>{e.form={},e.formVisible=!0},T=r=>{e.form=JSON.parse(JSON.stringify(r)),e.formVisible=!0},z=()=>{_.post("/type/add",e.form).then(r=>{r.code==="200"?(m.success("操作成功"),e.formVisible=!1,p()):m.error(r.msg)})},M=r=>{e.viewContent=r,e.viewVisible=!0},O=()=>{_.put("/type/update",e.form).then(r=>{r.code==="200"&&(m.success("操作成功"),e.formVisible=!1,p())})},D=()=>{e.form.id?O():z()},F=r=>{N.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(t=>{_.delete("/type/delete/"+r).then(d=>{d.code==="200"?(m.success("删除成功"),p()):m.error(d.msg)})}).catch(t=>{console.error(t)})},J=()=>{if(!e.ids.length){m.warning("请选择数据");return}N.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(r=>{_.delete("/type/delete/batch",{data:e.ids}).then(t=>{t.code==="200"?(m.success("操作成功"),p()):m.error(t.msg)})}).catch(r=>{console.error(r)})},R=r=>{e.ids=r.map(t=>t.id)},$=()=>{e.name=null,p()},q=r=>{e.form.img=r.data},H=r=>{e.form.img2=r.data},L=r=>{e.form.img1=r.data};return p(),(r,t)=>{const d=Y,i=Z,n=le,v=re,P=ee,A=te,u=se,j=de,x=pe,G=ne,h=oe;return f(),C("div",null,[b("div",fe,[o(d,{modelValue:e.name,"onUpdate:modelValue":t[0]||(t[0]=l=>e.name=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入房间类型查询"},null,8,["modelValue"]),o(i,{type:"info",plain:"",onClick:p},{default:a(()=>t[9]||(t[9]=[s("查询")])),_:1,__:[9]}),o(i,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:$},{default:a(()=>t[10]||(t[10]=[s("重置")])),_:1,__:[10]})]),e.user.role==="BUSINESS"?(f(),C("div",ce,[o(i,{type:"primary",plain:"",onClick:B},{default:a(()=>t[11]||(t[11]=[s("新增")])),_:1,__:[11]}),o(i,{type:"danger",plain:"",onClick:J},{default:a(()=>t[12]||(t[12]=[s("批量删除")])),_:1,__:[12]})])):g("",!0),b("div",ge,[o(P,{stripe:"",data:e.tableData,onSelectionChange:R,"tooltip-effect":"light myEffect"},{default:a(()=>[o(n,{type:"selection",width:"55"}),o(n,{prop:"businessName",label:"所属商家"}),o(n,{prop:"name",label:"房间名称"}),o(n,{prop:"img",label:"封面"},{default:a(l=>[l.row.img?(f(),w(v,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.img,"preview-src-list":[l.row.img],"preview-teleported":""},null,8,["src","preview-src-list"])):g("",!0)]),_:1}),o(n,{prop:"description",label:"类型简介","show-overflow-tooltip":""}),o(n,{prop:"price",label:"房间价格"}),o(n,{prop:"content",label:"详细介绍",width:"100"},{default:a(l=>[o(i,{type:"primary",onClick:E=>M(l.row.content)},{default:a(()=>t[13]||(t[13]=[s("点击查看")])),_:2,__:[13]},1032,["onClick"])]),_:1}),o(n,{prop:"total",label:"房间总数"}),o(n,{prop:"num",label:"空闲房间"}),o(n,{prop:"img1",label:"图片1"},{default:a(l=>[l.row.img1?(f(),w(v,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.img1,"preview-src-list":[l.row.img1],"preview-teleported":""},null,8,["src","preview-src-list"])):g("",!0)]),_:1}),o(n,{prop:"img2",label:"图片2"},{default:a(l=>[l.row.img2?(f(),w(v,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.img2,"preview-src-list":[l.row.img2],"preview-teleported":""},null,8,["src","preview-src-list"])):g("",!0)]),_:1}),o(n,{label:"操作",width:"100",fixed:"right"},{default:a(l=>[e.user.role==="BUSINESS"?(f(),w(i,{key:0,type:"primary",circle:"",icon:c(ae),onClick:E=>T(l.row)},null,8,["icon","onClick"])):g("",!0),o(i,{type:"danger",circle:"",icon:c(ie),onClick:E=>F(l.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),e.total?(f(),C("div",_e,[o(A,{onCurrentChange:p,background:"",layout:"prev, pager, next","page-size":e.pageSize,"current-page":e.pageNum,"onUpdate:currentPage":t[1]||(t[1]=l=>e.pageNum=l),total:e.total},null,8,["page-size","current-page","total"])])):g("",!0),o(h,{title:"房间类型信息",modelValue:e.formVisible,"onUpdate:modelValue":t[7]||(t[7]=l=>e.formVisible=l),width:"50%","destroy-on-close":""},{footer:a(()=>[b("span",ye,[o(i,{onClick:t[6]||(t[6]=l=>e.formVisible=!1)},{default:a(()=>t[17]||(t[17]=[s("取 消")])),_:1,__:[17]}),o(i,{type:"primary",onClick:D},{default:a(()=>t[18]||(t[18]=[s("确 定")])),_:1,__:[18]})])]),default:a(()=>[o(G,{ref_key:"formRef",ref:U,model:e.form,rules:e.rules,"label-width":"110px",style:{padding:"20px"}},{default:a(()=>[o(u,{prop:"name",label:"类型名称"},{default:a(()=>[o(d,{modelValue:e.form.name,"onUpdate:modelValue":t[2]||(t[2]=l=>e.form.name=l),placeholder:"请输入类型名称"},null,8,["modelValue"])]),_:1}),o(u,{prop:"description",label:"房间类型简介"},{default:a(()=>[o(d,{type:"textarea",rows:4,modelValue:e.form.description,"onUpdate:modelValue":t[3]||(t[3]=l=>e.form.description=l),placeholder:"请输入类型名称"},null,8,["modelValue"])]),_:1}),o(u,{prop:"price",label:"房间价格"},{default:a(()=>[o(j,{modelValue:e.form.price,"onUpdate:modelValue":t[4]||(t[4]=l=>e.form.price=l),min:100},null,8,["modelValue"])]),_:1}),o(u,{prop:"content",label:"房间详细介绍"},{default:a(()=>[b("div",be,[o(c(me),{style:{"border-bottom":"1px solid #ccc"},editor:V.value,mode:S},null,8,["editor"]),o(c(ue),{style:{height:"500px","overflow-y":"hidden"},modelValue:e.form.content,"onUpdate:modelValue":t[5]||(t[5]=l=>e.form.content=l),mode:S,defaultConfig:k,onOnCreated:I},null,8,["modelValue"])])]),_:1}),o(u,{prop:"img",label:"封面"},{default:a(()=>[o(x,{action:c(y)+"/files/upload","on-success":q,"list-type":"picture"},{default:a(()=>[o(i,{type:"primary"},{default:a(()=>t[14]||(t[14]=[s("上传封面")])),_:1,__:[14]})]),_:1},8,["action"])]),_:1}),o(u,{prop:"img1",label:"小图1"},{default:a(()=>[o(x,{action:c(y)+"/files/upload","on-success":L,"list-type":"picture"},{default:a(()=>[o(i,{type:"primary"},{default:a(()=>t[15]||(t[15]=[s("上传小图1")])),_:1,__:[15]})]),_:1},8,["action"])]),_:1}),o(u,{prop:"img2",label:"小图2"},{default:a(()=>[o(x,{action:c(y)+"/files/upload","on-success":H,"list-type":"picture"},{default:a(()=>[o(i,{type:"primary"},{default:a(()=>t[16]||(t[16]=[s("上传小图2")])),_:1,__:[16]})]),_:1},8,["action"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),o(h,{title:"详细介绍",modelValue:e.viewVisible,"onUpdate:modelValue":t[8]||(t[8]=l=>e.viewVisible=l),width:"50%","destroy-on-close":""},{default:a(()=>[b("div",{innerHTML:e.viewContent,style:{padding:"20px"}},null,8,we)]),_:1},8,["modelValue"])])}}};export{He as default};
