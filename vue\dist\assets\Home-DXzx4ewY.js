/* empty css                *//* empty css                      */import{r as _}from"./request-BNOh435t.js";import{r as x,c as n,a as e,t as r,b as u,w as m,v as f,E as g,o,F as y,x as v,m as h,y as N,j as E}from"./index-DXyN40AS.js";const k={class:"card",style:{"margin-bottom":"5px"}},w={style:{display:"flex"}},B={class:"card",style:{flex:"50%",height:"450px"}},I={__name:"Home",setup(D){const s=x({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),noticeData:[]});return(()=>{_.get("/notice/selectAll").then(t=>{t.code==="200"?s.noticeData=t.data:g.error(t.msg)})})(),(t,a)=>{var i;const c=N,d=f;return o(),n("div",null,[e("div",k,"您好！"+r((i=s.user)==null?void 0:i.name)+"，欢迎使用本系统！",1),e("div",w,[e("div",B,[a[0]||(a[0]=e("div",{style:{"font-weight":"bold","font-size":"18px",padding:"10px 0 30px 10px"}},"系统公告",-1)),u(d,{style:{"max-width":"600px"}},{default:m(()=>[(o(!0),n(y,null,v(s.noticeData,(l,p)=>(o(),h(c,{key:p,timestamp:l.time},{default:m(()=>[E(r(l.content),1)]),_:2},1032,["timestamp"]))),128))]),_:1})]),a[1]||(a[1]=e("div",{style:{flex:"50%"}},null,-1))])])}}};export{I as default};
