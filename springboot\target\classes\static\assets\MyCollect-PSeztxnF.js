/* empty css                *//* empty css            */import{r as x}from"./request-BNOh435t.js";import{r as y,c as _,a as t,t as a,b as e,w as n,a0 as v,E as w,h as c,o as r,F as C,x as k,m as I,a2 as b,k as B,j as D}from"./index-DXyN40AS.js";const E={style:{width:"80%",margin:"20px auto","min-height":"600px"}},N={style:{color:"darkorchid","font-size":"18px","margin-top":"20px"}},z={style:{"margin-top":"20px"}},L={class:"front_card"},O=["onClick","src"],S={style:{padding:"10px"}},V={style:{display:"flex","align-items":"center"}},F={style:{"font-weight":"bold","font-size":"16px","margin-left":"5px"},class:"line1"},G={style:{display:"flex","align-items":"center","margin-top":"10px"}},M={style:{color:"#8a8a8a","margin-left":"5px"},class:"line1"},T={style:{display:"flex","align-items":"center","margin-top":"10px"}},j={style:{"margin-left":"5px"}},q={style:{"font-weight":"bold","font-size":"18px",color:"red"}},Q={__name:"MyCollect",setup(A){const l=y({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),collectData:[]});(()=>{x.get("/collect/selectAll",{params:{userId:l.user.id}}).then(o=>{o.code==="200"?l.collectData=o.data:w.error(o.msg)})})();const p=o=>{location.href=o};return(o,d)=>{const m=c("OfficeBuilding"),i=B,f=c("LocationInformation"),g=c("Goods"),u=b,h=v;return r(),_("div",E,[t("div",N,"我的收藏 （"+a(l.collectData.length)+"）",1),t("div",z,[e(h,{gutter:20},{default:n(()=>[(r(!0),_(C,null,k(l.collectData,s=>(r(),I(u,{span:6,style:{"margin-bottom":"20px"}},{default:n(()=>[t("div",L,[t("img",{onClick:P=>p("/front/typeDetail?id="+s.typeId),src:s.typeImg,alt:"",style:{width:"100%",height:"200px","border-radius":"5px 5px 0 0",cursor:"pointer"}},null,8,O),t("div",S,[t("div",V,[e(i,{size:"20"},{default:n(()=>[e(m)]),_:1}),t("div",F,a(s.businessName),1)]),t("div",G,[e(i,null,{default:n(()=>[e(f)]),_:1}),t("div",M,"房间类型："+a(s.typeName),1)]),t("div",T,[e(i,null,{default:n(()=>[e(g)]),_:1}),t("div",j,[t("span",q,"￥"+a(s.typePrice),1),d[0]||(d[0]=D(" / 晚"))])])])])]),_:2},1024))),256))]),_:1})])])}}};export{Q as default};
