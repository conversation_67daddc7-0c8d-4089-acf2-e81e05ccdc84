/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css              *//* empty css                     *//* empty css            *//* empty css               *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                *//* empty css                     *//* empty css               */import{r as _}from"./request-BNOh435t.js";/* empty css                    */import{O as X,r as Y,c as h,a as v,n as m,b as e,z as Z,w as o,A as ee,B as le,C as te,D as re,E as f,o as u,j as d,G as oe,m as c,H as ae,t as k,P as se,u as b,I as ne,J as ie,K as de,L as pe,Q as ue,F as me,x as fe,R as ce,S as ge,M as be,T as _e,U as we,N as E}from"./index-DXyN40AS.js";const ye={class:"card",style:{"margin-bottom":"5px"}},Ve={class:"card",style:{"margin-bottom":"5px"}},ve={class:"card",style:{"margin-bottom":"5px"}},ke={style:{color:"red"}},xe={key:0,class:"card"},he={class:"dialog-footer"},je={__name:"Business",setup(Ue){const w="http://:9090",C=X(),r=Y({formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,name:null,provinceData:[],ids:[],rules:{username:[{required:!0,message:"请输入账号",trigger:"blur"}],name:[{required:!0,message:"请输入宾馆名称",trigger:"blur"}],phone:[{required:!0,message:"请输入宾馆电话",trigger:"blur"}],email:[{required:!0,message:"请输入宾馆邮箱",trigger:"blur"}],provinceId:[{required:!0,message:"请选择宾馆所在省份",trigger:"blur"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"}],content:[{required:!0,message:"请输入宾馆描述",trigger:"blur"}],price:[{required:!0,message:"请输入起始价格",trigger:"blur"}],img:[{required:!0,message:"请上传民宿图片",trigger:"blur"}],license:[{required:!0,message:"请上传经营许可证",trigger:"blur"}],front:[{required:!0,message:"请上传身份证正面",trigger:"blur"}],back:[{required:!0,message:"请上传身份证反面",trigger:"blur"}],leader:[{required:!0,message:"请输入负责人姓名",trigger:"blur"}],code:[{required:!0,message:"请输入负责人身份证号",trigger:"blur"}],status:[{required:!0,message:"请选择民宿状态",trigger:"blur"}]}});(()=>{_.get("province/selectAll").then(a=>{a.code==="200"?r.provinceData=a.data:f.error(a.msg)})})();const g=()=>{_.get("/business/selectPage",{params:{pageNum:r.pageNum,pageSize:r.pageSize,name:r.name}}).then(a=>{var t,i;a.code==="200"&&(r.tableData=((t=a.data)==null?void 0:t.list)||[],r.total=(i=a.data)==null?void 0:i.total)})},q=()=>{r.form={},r.formVisible=!0},N=a=>{r.form=JSON.parse(JSON.stringify(a)),r.formVisible=!0},S=()=>{_.post("/business/add",r.form).then(a=>{a.code==="200"?(f.success("操作成功"),r.formVisible=!1,g()):f.error(a.msg)})},B=()=>{_.put("/business/update",r.form).then(a=>{a.code==="200"&&(f.success("操作成功"),r.formVisible=!1,g())})},D=()=>{r.form.id?B():S()},I=a=>{E.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(t=>{_.delete("/business/delete/"+a).then(i=>{i.code==="200"?(f.success("删除成功"),g()):f.error(i.msg)})}).catch(t=>{console.error(t)})},z=()=>{if(!r.ids.length){f.warning("请选择数据");return}E.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(a=>{_.delete("/business/delete/batch",{data:r.ids}).then(t=>{t.code==="200"?(f.success("操作成功"),g()):f.error(t.msg)})}).catch(a=>{console.error(a)})},A=a=>{r.ids=a.map(t=>t.id)},F=a=>{r.form.avatar=a.data},P=a=>{r.form.img=a.data},R=a=>{r.form.license=a.data},T=a=>{r.form.front=a.data},O=a=>{r.form.back=a.data},J=()=>{r.name=null,g()};return g(),(a,t)=>{const i=Z,p=ee,s=oe,y=ae,x=se,L=le,M=te,n=pe,$=ce,G=ue,j=ge,V=be,U=we,H=_e,K=de,Q=re;return u(),h("div",null,[v("div",ye,[e(i,{modelValue:r.name,"onUpdate:modelValue":t[0]||(t[0]=l=>r.name=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入名称查询"},null,8,["modelValue"]),e(p,{type:"info",plain:"",onClick:g},{default:o(()=>t[16]||(t[16]=[d("查询")])),_:1,__:[16]}),e(p,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:J},{default:o(()=>t[17]||(t[17]=[d("重置")])),_:1,__:[17]})]),v("div",Ve,[e(p,{type:"primary",plain:"",onClick:q},{default:o(()=>t[18]||(t[18]=[d("新增")])),_:1,__:[18]}),e(p,{type:"danger",plain:"",onClick:z},{default:o(()=>t[19]||(t[19]=[d("批量删除")])),_:1,__:[19]})]),v("div",ve,[e(L,{stripe:"",data:r.tableData,onSelectionChange:A,"tooltip-effect":"dark myEffect"},{default:o(()=>[e(s,{type:"selection",width:"55"}),e(s,{prop:"avatar",label:"头像"},{default:o(l=>[l.row.avatar?(u(),c(y,{key:0,style:{width:"40px",height:"40px","border-radius":"50%",display:"block"},src:l.row.avatar,"preview-src-list":[l.row.avatar],"preview-teleported":""},null,8,["src","preview-src-list"])):m("",!0)]),_:1}),e(s,{prop:"img",label:"宾馆照片"},{default:o(l=>[l.row.img?(u(),c(y,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.img,"preview-src-list":[l.row.img],"preview-teleported":""},null,8,["src","preview-src-list"])):m("",!0)]),_:1}),e(s,{prop:"username",label:"账号"}),e(s,{prop:"name",label:"名称"}),e(s,{prop:"role",label:"角色"}),e(s,{prop:"phone",label:"电话"}),e(s,{prop:"email",label:"邮箱","show-overflow-tooltip":""}),e(s,{prop:"provinceName",label:"所属省份"}),e(s,{prop:"address",label:"地址","show-overflow-tooltip":""}),e(s,{prop:"content",label:"描述","show-overflow-tooltip":""}),e(s,{prop:"price",label:"起始价格"},{default:o(l=>[v("span",ke,"￥"+k(l.row.price),1)]),_:1}),e(s,{prop:"link",label:"官网","show-overflow-tooltip":""}),e(s,{prop:"license",label:"许可证"},{default:o(l=>[l.row.license?(u(),c(y,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.license,"preview-src-list":[l.row.license],"preview-teleported":""},null,8,["src","preview-src-list"])):m("",!0)]),_:1}),e(s,{prop:"leader",label:"负责人"}),e(s,{prop:"code",label:"身份证号","show-overflow-tooltip":""}),e(s,{prop:"front",label:"身份证正面"},{default:o(l=>[l.row.front?(u(),c(y,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.front,"preview-src-list":[l.row.front],"preview-teleported":""},null,8,["src","preview-src-list"])):m("",!0)]),_:1}),e(s,{prop:"back",label:"身份证反面"},{default:o(l=>[l.row.back?(u(),c(y,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.back,"preview-src-list":[l.row.back],"preview-teleported":""},null,8,["src","preview-src-list"])):m("",!0)]),_:1}),e(s,{prop:"status",label:"状态",fixed:"right"},{default:o(l=>[l.row.status==="待审核"?(u(),c(x,{key:0,type:"warning"},{default:o(()=>[d(k(l.row.status),1)]),_:2},1024)):m("",!0),l.row.status==="通过"?(u(),c(x,{key:1,type:"success"},{default:o(()=>[d(k(l.row.status),1)]),_:2},1024)):m("",!0),l.row.status==="拒绝"?(u(),c(x,{key:2,type:"danger"},{default:o(()=>[d(k(l.row.status),1)]),_:2},1024)):m("",!0)]),_:1}),e(s,{label:"操作",width:"100",fixed:"right"},{default:o(l=>[e(p,{type:"primary",circle:"",icon:b(ne),onClick:W=>N(l.row)},null,8,["icon","onClick"]),e(p,{type:"danger",circle:"",icon:b(ie),onClick:W=>I(l.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),r.total?(u(),h("div",xe,[e(M,{onCurrentChange:g,background:"",layout:"prev, pager, next","page-size":r.pageSize,"current-page":r.pageNum,"onUpdate:currentPage":t[1]||(t[1]=l=>r.pageNum=l),total:r.total},null,8,["page-size","current-page","total"])])):m("",!0),e(Q,{title:"商家信息",modelValue:r.formVisible,"onUpdate:modelValue":t[15]||(t[15]=l=>r.formVisible=l),width:"40%","destroy-on-close":""},{footer:o(()=>[v("span",he,[e(p,{onClick:t[14]||(t[14]=l=>r.formVisible=!1)},{default:o(()=>t[25]||(t[25]=[d("取 消")])),_:1,__:[25]}),e(p,{type:"primary",onClick:D},{default:o(()=>t[26]||(t[26]=[d("确 定")])),_:1,__:[26]})])]),default:o(()=>[e(K,{ref_key:"formRef",ref:C,model:r.form,rules:r.rules,"label-width":"100px",style:{padding:"20px"}},{default:o(()=>[e(n,{prop:"username",label:"账户"},{default:o(()=>[e(i,{modelValue:r.form.username,"onUpdate:modelValue":t[2]||(t[2]=l=>r.form.username=l),placeholder:"请输入账户名称"},null,8,["modelValue"])]),_:1}),e(n,{prop:"name",label:"宾馆"},{default:o(()=>[e(i,{modelValue:r.form.name,"onUpdate:modelValue":t[3]||(t[3]=l=>r.form.name=l),placeholder:"请输入宾馆名称"},null,8,["modelValue"])]),_:1}),e(n,{prop:"phone",label:"宾馆电话"},{default:o(()=>[e(i,{modelValue:r.form.phone,"onUpdate:modelValue":t[4]||(t[4]=l=>r.form.phone=l),placeholder:"请输入宾馆电话"},null,8,["modelValue"])]),_:1}),e(n,{prop:"email",label:"宾馆邮箱"},{default:o(()=>[e(i,{modelValue:r.form.email,"onUpdate:modelValue":t[5]||(t[5]=l=>r.form.email=l),placeholder:"请输入宾馆邮箱"},null,8,["modelValue"])]),_:1}),e(n,{prop:"provinceId",label:"所属省份"},{default:o(()=>[e(G,{modelValue:r.form.provinceId,"onUpdate:modelValue":t[6]||(t[6]=l=>r.form.provinceId=l),placeholder:"请选择省份",style:{width:"100%"}},{default:o(()=>[(u(!0),h(me,null,fe(r.provinceData,l=>(u(),c($,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n,{prop:"address",label:"地址"},{default:o(()=>[e(i,{type:"textarea",rows:4,modelValue:r.form.address,"onUpdate:modelValue":t[7]||(t[7]=l=>r.form.address=l),placeholder:"请输入宾馆地址"},null,8,["modelValue"])]),_:1}),e(n,{prop:"content",label:"描述"},{default:o(()=>[e(i,{type:"textarea",rows:5,modelValue:r.form.content,"onUpdate:modelValue":t[8]||(t[8]=l=>r.form.content=l),placeholder:"请输入宾馆描述"},null,8,["modelValue"])]),_:1}),e(n,{prop:"price",label:"价格"},{default:o(()=>[e(j,{modelValue:r.form.price,"onUpdate:modelValue":t[9]||(t[9]=l=>r.form.price=l),min:100},null,8,["modelValue"])]),_:1}),e(n,{prop:"img",label:"宾馆图片"},{default:o(()=>[e(V,{action:b(w)+"/files/upload","on-success":P,"list-type":"picture"},{default:o(()=>[e(p,{type:"primary"},{default:o(()=>t[20]||(t[20]=[d("上传宾馆图片")])),_:1,__:[20]})]),_:1},8,["action"])]),_:1}),e(n,{prop:"license",label:"营业执照"},{default:o(()=>[e(V,{action:b(w)+"/files/upload","on-success":R,"list-type":"picture"},{default:o(()=>[e(p,{type:"primary"},{default:o(()=>t[21]||(t[21]=[d("上传营业执照")])),_:1,__:[21]})]),_:1},8,["action"])]),_:1}),e(n,{prop:"leader",label:"负责人姓名"},{default:o(()=>[e(i,{modelValue:r.form.leader,"onUpdate:modelValue":t[10]||(t[10]=l=>r.form.leader=l),placeholder:"请输入负责人姓名"},null,8,["modelValue"])]),_:1}),e(n,{prop:"code",label:"身份证号"},{default:o(()=>[e(i,{modelValue:r.form.code,"onUpdate:modelValue":t[11]||(t[11]=l=>r.form.code=l),placeholder:"请输入负责人身份证号"},null,8,["modelValue"])]),_:1}),e(n,{prop:"front",label:"身份证正面"},{default:o(()=>[e(V,{action:b(w)+"/files/upload","on-success":T,"list-type":"picture"},{default:o(()=>[e(p,{type:"danger"},{default:o(()=>t[22]||(t[22]=[d("上传身份证正面照片")])),_:1,__:[22]})]),_:1},8,["action"])]),_:1}),e(n,{prop:"back",label:"身份证反面"},{default:o(()=>[e(V,{action:b(w)+"/files/upload","on-success":O,"list-type":"picture"},{default:o(()=>[e(p,{type:"danger"},{default:o(()=>t[23]||(t[23]=[d("上传身份证反面照片")])),_:1,__:[23]})]),_:1},8,["action"])]),_:1}),e(n,{prop:"link",label:"官网链接"},{default:o(()=>[e(i,{modelValue:r.form.link,"onUpdate:modelValue":t[12]||(t[12]=l=>r.form.link=l),placeholder:"请输入官网链接"},null,8,["modelValue"])]),_:1}),e(n,{prop:"avatar",label:"头像"},{default:o(()=>[e(V,{action:b(w)+"/files/upload","on-success":F,"list-type":"picture"},{default:o(()=>[e(p,{type:"primary"},{default:o(()=>t[24]||(t[24]=[d("点击上传")])),_:1,__:[24]})]),_:1},8,["action"])]),_:1}),e(n,{prop:"status",label:"宾馆状态"},{default:o(()=>[e(H,{modelValue:r.form.status,"onUpdate:modelValue":t[13]||(t[13]=l=>r.form.status=l),fill:"#A3A6AD"},{default:o(()=>[e(U,{label:"通过",value:"通过"}),e(U,{label:"拒绝",value:"拒绝"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}};export{je as default};
