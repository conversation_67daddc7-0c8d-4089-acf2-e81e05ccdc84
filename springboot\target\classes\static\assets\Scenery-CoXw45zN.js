/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css            *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                   *//* empty css              *//* empty css                     *//* empty css                *//* empty css             *//* empty css                     *//* empty css               *//* empty css              */import{r as c}from"./request-BNOh435t.js";/* empty css                    */import{O as K,r as Q,V as W,W as X,c as w,a as f,n as x,b as t,z as Y,w as n,A as Z,B as ee,C as oe,D as te,E as s,o as g,j as p,G as le,m as k,H as ae,X as ne,u as _,I as re,J as ie,K as de,L as se,M as me,Q as pe,F as ue,x as ce,R as fe,N as E}from"./index-DXyN40AS.js";import{T as ge,E as _e}from"./index.esm-tRL6DuoP.js";import"./index.esm-CA4zuM-h.js";const be={class:"card",style:{"margin-bottom":"5px"}},ye={class:"card",style:{"margin-bottom":"5px"}},we={class:"card",style:{"margin-bottom":"5px"}},Ve={key:0,class:"card"},ve={style:{border:"1px solid #ccc",width:"100%"}},Ce={class:"dialog-footer"},he=["innerHTML"],N="default",Ge={__name:"Scenery",setup(xe){const V="http://:9090",U=K(),e=Q({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),formVisible:!1,form:{},tableData:[],provinceData:[],pageNum:1,pageSize:5,total:0,name:null,ids:[],viewContent:null,viewVisible:null,rules:{name:[{required:!0,message:"请输入景点名称",trigger:"blur"}],description:[{required:!0,message:"请输入景点简介",trigger:"blur"}]}}),y=W(),v={MENU_CONF:{}};v.MENU_CONF.uploadImage={headers:{token:e.user.token},server:V+"/files/wang/upload",fieldName:"file"},X(()=>{const a=y.value;a!=null&&a.destroy()});const S=a=>{y.value=a},m=()=>{c.get("/scenery/selectPage",{params:{pageNum:e.pageNum,pageSize:e.pageSize,name:e.name}}).then(a=>{var o,i;a.code==="200"&&(e.tableData=((o=a.data)==null?void 0:o.list)||[],e.total=(i=a.data)==null?void 0:i.total)})};(()=>{c.get("province/selectAll").then(a=>{a.code==="200"?e.provinceData=a.data:s.error(a.msg)})})();const I=()=>{e.form={},e.formVisible=!0},B=a=>{e.form=JSON.parse(JSON.stringify(a)),e.formVisible=!0},D=()=>{c.post("/scenery/add",e.form).then(a=>{a.code==="200"?(s.success("操作成功"),e.formVisible=!1,m()):s.error(a.msg)})},O=a=>{e.viewContent=a,e.viewVisible=!0},z=()=>{c.put("/scenery/update",e.form).then(a=>{a.code==="200"&&(s.success("操作成功"),e.formVisible=!1,m())})},M=()=>{e.form.id?z():D()},T=a=>{E.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(o=>{c.delete("/scenery/delete/"+a).then(i=>{i.code==="200"?(s.success("删除成功"),m()):s.error(i.msg)})}).catch(o=>{console.error(o)})},F=()=>{if(!e.ids.length){s.warning("请选择数据");return}E.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(a=>{c.delete("/scenery/delete/batch",{data:e.ids}).then(o=>{o.code==="200"?(s.success("操作成功"),m()):s.error(o.msg)})}).catch(a=>{console.error(a)})},R=a=>{e.ids=a.map(o=>o.id)},P=()=>{e.name=null,m()},J=a=>{e.form.img=a.data};return m(),(a,o)=>{const i=Y,r=Z,d=le,L=ae,C=ne,$=ee,q=oe,u=se,A=me,H=fe,j=pe,G=de,h=te;return g(),w("div",null,[f("div",be,[t(i,{modelValue:e.name,"onUpdate:modelValue":o[0]||(o[0]=l=>e.name=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入景点名称查询"},null,8,["modelValue"]),t(r,{type:"info",plain:"",onClick:m},{default:n(()=>o[10]||(o[10]=[p("查询")])),_:1,__:[10]}),t(r,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:P},{default:n(()=>o[11]||(o[11]=[p("重置")])),_:1,__:[11]})]),f("div",ye,[t(r,{type:"primary",plain:"",onClick:I},{default:n(()=>o[12]||(o[12]=[p("新增")])),_:1,__:[12]}),t(r,{type:"danger",plain:"",onClick:F},{default:n(()=>o[13]||(o[13]=[p("批量删除")])),_:1,__:[13]})]),f("div",we,[t($,{stripe:"",data:e.tableData,onSelectionChange:R,"tooltip-effect":"light myEffect"},{default:n(()=>[t(d,{type:"selection",width:"55"}),t(d,{prop:"img",label:"景点图片",width:"80"},{default:n(l=>[l.row.img?(g(),k(L,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.img,"preview-src-list":[l.row.img],"preview-teleported":""},null,8,["src","preview-src-list"])):x("",!0)]),_:1}),t(d,{prop:"name",label:"景点名称",width:"120"}),t(d,{prop:"description",label:"景点简介","show-overflow-tooltip":""}),t(d,{prop:"content",label:"景点详情",width:"150"},{default:n(l=>[t(r,{type:"primary",onClick:b=>O(l.row.content)},{default:n(()=>o[14]||(o[14]=[p("查看详情")])),_:2,__:[14]},1032,["onClick"])]),_:1}),t(d,{prop:"provinceName",label:"所属省份",width:"100"}),t(d,{prop:"recommend",label:"推荐指数",width:"180"},{default:n(l=>[t(C,{modelValue:l.row.recommend,"onUpdate:modelValue":b=>l.row.recommend=b,disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(d,{prop:"views",label:"浏览量",width:"150"}),t(d,{label:"操作",width:"100",fixed:"right"},{default:n(l=>[t(r,{type:"primary",circle:"",icon:_(re),onClick:b=>B(l.row)},null,8,["icon","onClick"]),t(r,{type:"danger",circle:"",icon:_(ie),onClick:b=>T(l.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),e.total?(g(),w("div",Ve,[t(q,{onCurrentChange:m,background:"",layout:"prev, pager, next","page-size":e.pageSize,"current-page":e.pageNum,"onUpdate:currentPage":o[1]||(o[1]=l=>e.pageNum=l),total:e.total},null,8,["page-size","current-page","total"])])):x("",!0),t(h,{title:"热门景点信息",modelValue:e.formVisible,"onUpdate:modelValue":o[8]||(o[8]=l=>e.formVisible=l),width:"50%","destroy-on-close":""},{footer:n(()=>[f("span",Ce,[t(r,{onClick:o[7]||(o[7]=l=>e.formVisible=!1)},{default:n(()=>o[16]||(o[16]=[p("取 消")])),_:1,__:[16]}),t(r,{type:"primary",onClick:M},{default:n(()=>o[17]||(o[17]=[p("确 定")])),_:1,__:[17]})])]),default:n(()=>[t(G,{ref_key:"formRef",ref:U,model:e.form,rules:e.rules,"label-width":"100px",style:{padding:"20px"}},{default:n(()=>[t(u,{prop:"name",label:"景点名称"},{default:n(()=>[t(i,{modelValue:e.form.name,"onUpdate:modelValue":o[2]||(o[2]=l=>e.form.name=l),placeholder:"请输入景点名称"},null,8,["modelValue"])]),_:1}),t(u,{prop:"img",label:"景点图片"},{default:n(()=>[t(A,{action:_(V)+"/files/upload","on-success":J,"list-type":"picture"},{default:n(()=>[t(r,{type:"primary"},{default:n(()=>o[15]||(o[15]=[p("上传景点图片")])),_:1,__:[15]})]),_:1},8,["action"])]),_:1}),t(u,{prop:"recommend",label:"推荐指数"},{default:n(()=>[t(C,{modelValue:e.form.recommend,"onUpdate:modelValue":o[3]||(o[3]=l=>e.form.recommend=l)},null,8,["modelValue"])]),_:1}),t(u,{prop:"description",label:"景点简介"},{default:n(()=>[t(i,{type:"textarea",rows:4,modelValue:e.form.description,"onUpdate:modelValue":o[4]||(o[4]=l=>e.form.description=l),placeholder:"请输入景点简介"},null,8,["modelValue"])]),_:1}),t(u,{prop:"provinceId",label:"所属省份"},{default:n(()=>[t(j,{modelValue:e.form.provinceId,"onUpdate:modelValue":o[5]||(o[5]=l=>e.form.provinceId=l),placeholder:"请选择省份",style:{width:"100%"}},{default:n(()=>[(g(!0),w(ue,null,ce(e.provinceData,l=>(g(),k(H,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(u,{prop:"content",label:"景点详细介绍"},{default:n(()=>[f("div",ve,[t(_(ge),{style:{"border-bottom":"1px solid #ccc"},editor:y.value,mode:N},null,8,["editor"]),t(_(_e),{style:{height:"500px","overflow-y":"hidden"},modelValue:e.form.content,"onUpdate:modelValue":o[6]||(o[6]=l=>e.form.content=l),mode:N,defaultConfig:v,onOnCreated:S},null,8,["modelValue"])])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),t(h,{title:"详细介绍",modelValue:e.viewVisible,"onUpdate:modelValue":o[9]||(o[9]=l=>e.viewVisible=l),width:"50%","destroy-on-close":""},{default:n(()=>[f("div",{innerHTML:e.viewContent,style:{padding:"20px"}},null,8,he)]),_:1},8,["modelValue"])])}}};export{Ge as default};
