/* empty css                *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as d}from"./request-BNOh435t.js";/* empty css                    *//* empty css                */import{O as h,r as x,c as u,a as c,n as y,b as o,z as v,w as n,A as C,B as E,C as k,o as g,j as m,G as V,N as z,E as S}from"./index-DXyN40AS.js";const B={class:"card",style:{"margin-bottom":"5px"}},T={class:"card",style:{"margin-bottom":"5px"}},D={key:0,class:"card"},L={__name:"Registration",setup(O){h();const t=x({orderNo:null,status:null,total:0,pageNum:1,pageSize:5,registrationData:[],form:{}}),_=a=>{z.confirm("是否确认退房？","退房确认",{type:"warning"}).then(e=>{t.form=JSON.parse(JSON.stringify(a)),f()}).catch(e=>{console.error(e)})},i=()=>{d.get("/registration/selectPage",{params:{pageNum:t.pageNum,pageSize:t.pageSize,orderNo:t.orderNo}}).then(a=>{var e,s;a.code==="200"&&(t.registrationData=((e=a.data)==null?void 0:e.list)||[],t.total=(s=a.data)==null?void 0:s.total)})},f=()=>{d.put("/registration/update",t.form).then(a=>{a.code==="200"&&(S.success("操作成功"),t.formVisible=!1,i())})},N=()=>{t.orderNo=null,i()};return i(),(a,e)=>{const s=v,p=C,r=V,b=E,w=k;return g(),u("div",null,[c("div",B,[o(s,{modelValue:t.orderNo,"onUpdate:modelValue":e[0]||(e[0]=l=>t.orderNo=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入订单编号查询"},null,8,["modelValue"]),o(p,{type:"info",plain:"",onClick:i},{default:n(()=>e[2]||(e[2]=[m("查询")])),_:1,__:[2]}),o(p,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:N},{default:n(()=>e[3]||(e[3]=[m("重置")])),_:1,__:[3]})]),c("div",T,[o(b,{stripe:"",data:t.registrationData},{default:n(()=>[o(r,{prop:"orderNo",label:"订单号","show-overflow-tooltip":""}),o(r,{prop:"userName",label:"下单用户",width:"120"}),o(r,{prop:"businessName",label:"宾馆名称",width:"120"}),o(r,{prop:"typeName",label:"房间类型",width:"120"}),o(r,{prop:"roomName",label:"房间编号",width:"120"}),o(r,{prop:"inTime",label:"登记时间","show-overflow-tooltip":""}),o(r,{prop:"outTime",label:"退房时间","show-overflow-tooltip":""}),o(r,{label:"操作",width:"180",fixed:"right"},{default:n(l=>[o(p,{disabled:l.row.outTime,type:"info",onClick:P=>_(l.row)},{default:n(()=>e[4]||(e[4]=[m("退房")])),_:2,__:[4]},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])]),t.total?(g(),u("div",D,[o(w,{onCurrentChange:i,background:"",layout:"prev, pager, next","page-size":t.pageSize,"current-page":t.pageNum,"onUpdate:currentPage":e[1]||(e[1]=l=>t.pageNum=l),total:t.total},null,8,["page-size","current-page","total"])])):y("",!0)])}}};export{L as default};
