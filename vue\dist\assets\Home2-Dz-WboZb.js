import{_ as m,i as p}from"./index-DABEre8E.js";import{r as o}from"./request-BNOh435t.js";/* empty css                */import{r as g,c as y,a as t,t as a,E as n,o as f}from"./index-DXyN40AS.js";const c="/assets/%E4%BD%99%E9%A2%9D2-Dm-NokfS.jpg",v="/assets/%E6%88%BF%E9%97%B4-s_kHtPap.jpg",u="/assets/%E7%89%B9%E4%BA%A72-7DJzVIMR.png",h="/assets/%E6%99%AF%E7%82%B9-CT5rNpqz.jpg",_="/assets/%E5%B8%96%E5%AD%902-Be97Rgv4.jpg",b={style:{display:"flex","grid-gap":"10px"}},D={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},B={style:{flex:"1","font-size":"20px"}},E={style:{"font-weight":"bold",color:"red"}},w={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},A={style:{flex:"1","font-size":"20px"}},L={style:{"font-weight":"bold",color:"red"}},z={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},S={style:{flex:"1","font-size":"20px"}},N={style:{"font-weight":"bold"}},T={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},k={style:{flex:"1","font-size":"20px"}},M={style:{"font-weight":"bold"}},O={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},j={style:{flex:"1","font-size":"20px"}},C={style:{"font-weight":"bold"}},F={class:"card",style:{flex:"1",display:"flex","align-items":"center",padding:"20px 0"}},I={style:{flex:"1","font-size":"20px"}},P={style:{"font-weight":"bold"}},U={__name:"Home2",setup(q){const i=g({baseData:{}});(()=>{o.get("/statistics/base").then(s=>{s.code==="200"?i.baseData=s.data:n.error(s.msg)})})(),(()=>{o.get("/statistics/line").then(s=>{if(s.code==="200"){let e=document.getElementById("line"),l=p(e);d.xAxis.data=s.data.xList,d.series[0].data=s.data.yList,l.setOption(d)}else n.error(s.msg)})})();let d={title:{text:"近一周平台每日宾馆流水折线图",subtext:"统计维度：最近一周",left:"center"},legend:{data:[],template:""},grid:{left:"3%",right:"4%",bottom:"3%",top:"20%",containLabel:!0},tooltip:{trigger:"item"},xAxis:{name:"日期",type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]},yAxis:{name:"流水金额",type:"value"},series:[{name:"流水金额",data:[820,932,901,934,1290,1330,1320],type:"line",smooth:!0,markLine:{data:[{type:"average",name:"最近7天宾馆预订流水金额平均值"}]},markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]}}]};(()=>{o.get("/statistics/pie").then(s=>{if(s.code==="200"){let e=document.getElementById("pie"),l=p(e);x.series[0].data=s.data,l.setOption(x)}else n.error(s.msg)})})(),(()=>{o.get("/statistics/bar").then(s=>{if(s.code==="200"){let e=document.getElementById("bar"),l=p(e);r.xAxis.data=s.data.xList,r.series[0].data=s.data.yList,l.setOption(r)}else n.error(s.msg)})})();let x={title:{text:"不同省份宾馆数量分布饼状图",subtext:"统计维度：省份信息",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"数量占比",type:"pie",radius:"50%",center:["50%","60%"],data:[{value:1048,name:"瑞幸咖啡"},{value:735,name:"雀巢咖啡"},{value:580,name:"星巴克咖啡"},{value:484,name:"栖巢咖啡"},{value:300,name:"小武哥咖啡"}]}]},r={title:{text:"不同商家营业额柱状图",subtext:"统计维度：宾馆名称",left:"center"},grid:{bottom:"10%",top:"25%"},legend:{orient:"vertical",left:"left"},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],name:"房间类型",axisLabel:{show:!0,interval:0,rotate:-60,inside:!1,margin:6}},yAxis:{type:"value",name:"空闲数量"},tooltip:{trigger:"item"},series:[{data:[120,200,150,80,70,110,130],type:"bar",itemStyle:{normal:{color:function(){return"#"+Math.floor(Math.random()*(256*256*256-1)).toString(16)}}}}]};return(s,e)=>(f(),y("div",null,[t("div",b,[t("div",D,[e[1]||(e[1]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:m,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",B,[e[0]||(e[0]=t("div",null,"宾馆总流水",-1)),t("div",E,"￥"+a(i.baseData.businessTotal),1)])]),t("div",w,[e[3]||(e[3]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:c,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",A,[e[2]||(e[2]=t("div",null,"特产总流水",-1)),t("div",L,"￥"+a(i.baseData.productTotal),1)])]),t("div",z,[e[5]||(e[5]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:v,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",S,[e[4]||(e[4]=t("div",null,"宾馆数量",-1)),t("div",N,a(i.baseData.businessNum),1)])]),t("div",T,[e[7]||(e[7]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:u,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",k,[e[6]||(e[6]=t("div",null,"特产种类",-1)),t("div",M,a(i.baseData.productNum),1)])]),t("div",O,[e[9]||(e[9]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:h,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",j,[e[8]||(e[8]=t("div",null,"热门景点",-1)),t("div",C,a(i.baseData.sceneryNum),1)])]),t("div",F,[e[11]||(e[11]=t("div",{style:{flex:"1","text-align":"center"}},[t("img",{src:_,alt:"",style:{width:"70px",height:"70px"}})],-1)),t("div",I,[e[10]||(e[10]=t("div",null,"帖子数量",-1)),t("div",P,a(i.baseData.articleNum),1)])])]),e[12]||(e[12]=t("div",{style:{"margin-top":"10px",height:"500px"},class:"card",id:"line"},null,-1)),e[13]||(e[13]=t("div",{style:{"margin-top":"10px",display:"flex","grid-gap":"10px"}},[t("div",{style:{flex:"2",height:"400px"},class:"card",id:"pie"}),t("div",{style:{flex:"3",height:"400px"},class:"card",id:"bar"})],-1))]))}};export{U as default};
