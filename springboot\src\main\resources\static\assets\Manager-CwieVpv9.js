/* empty css                */import{_ as A}from"./logo-DNOLg89h.js";/* empty css                *//* empty css                  *//* empty css               */import{_ as V,r as O,E as R,c as $,a,b as n,w as t,d as T,e as F,u as m,f as i,g as H,h as p,o as l,i as J,j as r,t as y,k as P,l as j,m as u,n as d,p as q,q as z,s as G}from"./index-DXyN40AS.js";const K={class:"manager-container"},L={class:"manager-header"},Q={class:"manager-header-center"},W={class:"manager-header-right"},X={style:{"padding-right":"20px",display:"flex","align-items":"center"}},Y=["src"],Z={style:{"margin-left":"5px",color:"white"}},c={style:{display:"flex"}},h={class:"manager-main-left"},ee={class:"manager-main-right"},te={__name:"Manager",setup(ne){const o=O({user:JSON.parse(localStorage.getItem("xm-user")||"{}")}),x=()=>{localStorage.removeItem("xm-user"),i.push("/login")},I=()=>{o.user=JSON.parse(localStorage.getItem("xm-user")||"{}")};return o.user.id||(x(),R.error("请登录！")),(re,e)=>{const S=J,N=T,w=p("arrow-down"),_=P,f=q,E=j,M=F,B=p("HomeFilled"),s=z,v=p("PieChart"),b=p("TurnOff"),k=G,D=p("Menu"),C=H,U=p("RouterView");return l(),$("div",K,[a("div",L,[e[10]||(e[10]=a("div",{class:"manager-header-left"},[a("img",{src:A,alt:""}),a("div",{class:"title"},"小型宾馆管理系统")],-1)),a("div",Q,[n(N,{separator:"/"},{default:t(()=>[n(S,{to:{path:"/manager/home"}},{default:t(()=>e[4]||(e[4]=[r("首页")])),_:1,__:[4]}),n(S,null,{default:t(()=>[r(y(m(i).currentRoute.value.meta.name),1)]),_:1})]),_:1})]),a("div",W,[n(M,{style:{cursor:"pointer"}},{dropdown:t(()=>[n(E,null,{default:t(()=>[o.user.role==="ADMIN"?(l(),u(f,{key:0,onClick:e[0]||(e[0]=g=>m(i).push("/manager/person"))},{default:t(()=>e[5]||(e[5]=[r("管理员资料")])),_:1,__:[5]})):d("",!0),o.user.role==="BUSINESS"?(l(),u(f,{key:1,onClick:e[1]||(e[1]=g=>m(i).push("/manager/personBusiness"))},{default:t(()=>e[6]||(e[6]=[r("宾馆资料")])),_:1,__:[6]})):d("",!0),o.user.role==="BUSINESS"?(l(),u(f,{key:2,onClick:e[2]||(e[2]=g=>m(i).push("/manager/authentication"))},{default:t(()=>e[7]||(e[7]=[r("资格认证")])),_:1,__:[7]})):d("",!0),n(f,{onClick:e[3]||(e[3]=g=>m(i).push("/manager/password"))},{default:t(()=>e[8]||(e[8]=[r("修改密码")])),_:1,__:[8]}),n(f,{onClick:x},{default:t(()=>e[9]||(e[9]=[r("退出登录")])),_:1,__:[9]})]),_:1})]),default:t(()=>[a("div",X,[a("img",{style:{width:"40px",height:"40px","border-radius":"50%"},src:o.user.avatar,alt:""},null,8,Y),a("span",Z,y(o.user.name),1),n(_,{color:"#fff"},{default:t(()=>[n(w)]),_:1})])]),_:1})])]),a("div",c,[a("div",h,[n(C,{"default-active":m(i).currentRoute.value.path,"default-openeds":["1","2"],router:""},{default:t(()=>[n(s,{index:"/manager/home"},{default:t(()=>[n(_,null,{default:t(()=>[n(B)]),_:1}),e[11]||(e[11]=a("span",null,"系统首页",-1))]),_:1,__:[11]}),o.user.role==="ADMIN"?(l(),u(s,{key:0,index:"/manager/home2"},{default:t(()=>[n(_,null,{default:t(()=>[n(v)]),_:1}),e[12]||(e[12]=a("span",null,"数据统计",-1))]),_:1,__:[12]})):d("",!0),o.user.role==="BUSINESS"?(l(),u(s,{key:1,index:"/manager/homeBusiness"},{default:t(()=>[n(_,null,{default:t(()=>[n(v)]),_:1}),e[13]||(e[13]=a("span",null,"数据统计",-1))]),_:1,__:[13]})):d("",!0),o.user.role==="ADMIN"||o.user.role==="BUSINESS"&&o.user.status==="通过"?(l(),u(k,{key:2,index:"1"},{title:t(()=>[n(_,null,{default:t(()=>[n(b)]),_:1}),e[14]||(e[14]=a("span",null,"信息管理",-1))]),default:t(()=>[o.user.role==="ADMIN"?(l(),u(s,{key:0,index:"/manager/notice"},{default:t(()=>e[15]||(e[15]=[r("系统公告")])),_:1,__:[15]})):d("",!0),o.user.role==="ADMIN"?(l(),u(s,{key:1,index:"/manager/province"},{default:t(()=>e[16]||(e[16]=[r("所在省份")])),_:1,__:[16]})):d("",!0),n(s,{index:"/manager/type"},{default:t(()=>e[17]||(e[17]=[r("房间类型")])),_:1,__:[17]}),n(s,{index:"/manager/room"},{default:t(()=>e[18]||(e[18]=[r("房间信息")])),_:1,__:[18]}),o.user.role==="BUSINESS"?(l(),u(s,{key:2,index:"/manager/orders"},{default:t(()=>e[19]||(e[19]=[r("房间订单")])),_:1,__:[19]})):d("",!0),o.user.role==="BUSINESS"?(l(),u(s,{key:3,index:"/manager/registration"},{default:t(()=>e[20]||(e[20]=[r("入住登记")])),_:1,__:[20]})):d("",!0),n(s,{index:"/manager/scenery"},{default:t(()=>e[21]||(e[21]=[r("热门景点")])),_:1,__:[21]}),n(s,{index:"/manager/article"},{default:t(()=>e[22]||(e[22]=[r("旅游帖子")])),_:1,__:[22]}),n(s,{index:"/manager/comment"},{default:t(()=>e[23]||(e[23]=[r("帖子评论")])),_:1,__:[23]}),n(s,{index:"/manager/product"},{default:t(()=>e[24]||(e[24]=[r("当地特产")])),_:1,__:[24]}),n(s,{index:"/manager/productOrders"},{default:t(()=>e[25]||(e[25]=[r("特产订单")])),_:1,__:[25]}),o.user.role==="ADMIN"?(l(),u(s,{key:4,index:"/manager/feedback"},{default:t(()=>e[26]||(e[26]=[r("反馈建议")])),_:1,__:[26]})):d("",!0)]),_:1})):d("",!0),o.user.role==="ADMIN"?(l(),u(k,{key:3,index:"2"},{title:t(()=>[n(_,null,{default:t(()=>[n(D)]),_:1}),e[27]||(e[27]=a("span",null,"用户管理",-1))]),default:t(()=>[n(s,{index:"/manager/admin"},{default:t(()=>e[28]||(e[28]=[r("管理员信息")])),_:1,__:[28]}),n(s,{index:"/manager/user"},{default:t(()=>e[29]||(e[29]=[r("用户信息")])),_:1,__:[29]}),n(s,{index:"/manager/business"},{default:t(()=>e[30]||(e[30]=[r("商家信息")])),_:1,__:[30]})]),_:1})):d("",!0)]),_:1},8,["default-active"])]),a("div",ee,[n(U,{onUpdateUser:I})])])])}}},ie=V(te,[["__scopeId","data-v-cfc1f5e4"]]);export{ie as default};
