/* empty css                *//* empty css                  *//* empty css              *//* empty css               *//* empty css                 */import{r as h}from"./request-BNOh435t.js";import{r as k,c as i,b as a,w as t,K as B,h as I,o as u,a as S,L as N,M as w,u as c,m as C,k as F,A as P,j as _,z as J,E as f}from"./index-DXyN40AS.js";const M={style:{width:"50%"},class:"card"},O=["src"],j={style:{"text-align":"center"}},H={__name:"PersonBusiness",emits:["updateUser"],setup(q,{emit:V}){const d="http://:9090",e=k({user:JSON.parse(localStorage.getItem("xm-user")||"{}")}),b=o=>{e.user.avatar=o.data},g=o=>{e.user.img=o.data},v=V,y=()=>{e.user.role==="BUSINESS"&&h.put("/business/update",e.user).then(o=>{o.code==="200"?(f.success("保存成功"),localStorage.setItem("xm-user",JSON.stringify(e.user)),v("updateUser")):f.error(o.msg)})};return(o,l)=>{const U=I("Plus"),E=F,p=w,r=N,m=P,n=J,x=B;return u(),i("div",M,[a(x,{ref:"user",model:e.user,"label-width":"70px",style:{padding:"20px"}},{default:t(()=>[a(r,{prop:"avatar",label:"头像"},{default:t(()=>[a(p,{action:c(d)+"/files/upload","on-success":b,"show-file-list":!1,class:"avatar-uploader"},{default:t(()=>[e.user.avatar?(u(),i("img",{key:0,src:e.user.avatar,class:"avatar"},null,8,O)):(u(),C(E,{key:1,class:"avatar-uploader-icon"},{default:t(()=>[a(U)]),_:1}))]),_:1},8,["action"])]),_:1}),a(r,{prop:"img",label:"宾馆图片"},{default:t(()=>[a(p,{action:c(d)+"/files/upload","on-success":g,"list-type":"picture"},{default:t(()=>[a(m,{type:"primary"},{default:t(()=>l[4]||(l[4]=[_("上传宾馆图片")])),_:1,__:[4]})]),_:1},8,["action"])]),_:1}),a(r,{prop:"username",label:"用户名"},{default:t(()=>[a(n,{disabled:"",modelValue:e.user.username,"onUpdate:modelValue":l[0]||(l[0]=s=>e.user.username=s),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),a(r,{prop:"name",label:"宾馆名称"},{default:t(()=>[a(n,{disabled:"",modelValue:e.user.name,"onUpdate:modelValue":l[1]||(l[1]=s=>e.user.name=s),placeholder:"请输入宾馆名称"},null,8,["modelValue"])]),_:1}),a(r,{prop:"phone",label:"电话"},{default:t(()=>[a(n,{modelValue:e.user.phone,"onUpdate:modelValue":l[2]||(l[2]=s=>e.user.phone=s),placeholder:"请输入电话"},null,8,["modelValue"])]),_:1}),a(r,{prop:"email",label:"邮箱"},{default:t(()=>[a(n,{modelValue:e.user.email,"onUpdate:modelValue":l[3]||(l[3]=s=>e.user.email=s),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),S("div",j,[a(m,{type:"primary",onClick:y},{default:t(()=>l[5]||(l[5]=[_("保 存")])),_:1,__:[5]})])]),_:1},8,["model"])])}}};export{H as default};
