/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                     *//* empty css            *//* empty css               *//* empty css                  *//* empty css                   *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as f}from"./request-BNOh435t.js";/* empty css                    */import{r as $,c as y,a as k,n as c,b as l,z as G,w as n,A as L,B as M,C as j,D as q,E as s,o as i,j as d,G as K,m as _,P as Q,t as w,u as v,I as H,J as W,K as X,L as Y,Q as Z,F as ee,x as te,R as le,T as oe,U as ae,N as E}from"./index-DXyN40AS.js";const ne={class:"card",style:{"margin-bottom":"5px"}},re={key:0,class:"card",style:{"margin-bottom":"5px"}},se={class:"card",style:{"margin-bottom":"5px"}},ie={key:1,class:"card"},de={class:"dialog-footer"},he={__name:"Room",setup(me){const e=$({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,name:null,ids:[],typeData:[]});(()=>{f.get("type/selectAll",{params:{businessId:e.user.id}}).then(a=>{a.code==="200"?e.typeData=a.data:s.error(a.msg)})})();const m=()=>{f.get("/room/selectPage",{params:{pageNum:e.pageNum,pageSize:e.pageSize,name:e.name}}).then(a=>{var t,r;a.code==="200"&&(e.tableData=((t=a.data)==null?void 0:t.list)||[],e.total=(r=a.data)==null?void 0:r.total)})},S=()=>{e.form={},e.formVisible=!0},x=a=>{e.form=JSON.parse(JSON.stringify(a)),e.formVisible=!0},C=()=>{f.post("/room/add",e.form).then(a=>{a.code==="200"?(s.success("操作成功"),e.formVisible=!1,m()):s.error(a.msg)})},N=()=>{f.put("/room/update",e.form).then(a=>{a.code==="200"&&(s.success("操作成功"),e.formVisible=!1,m())})},h=()=>{e.form.id?N():C()},B=a=>{E.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(t=>{f.delete("/room/delete/"+a).then(r=>{r.code==="200"?(s.success("删除成功"),m()):s.error(r.msg)})}).catch(t=>{console.error(t)})},D=()=>{if(!e.ids.length){s.warning("请选择数据");return}E.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(a=>{f.delete("/room/delete/batch",{data:e.ids}).then(t=>{t.code==="200"?(s.success("操作成功"),m()):s.error(t.msg)})}).catch(a=>{console.error(a)})},I=a=>{e.ids=a.map(t=>t.id)},U=()=>{e.name=null,m()};return m(),(a,t)=>{const r=G,p=L,u=K,b=Q,z=M,T=j,A=le,F=Z,g=Y,V=ae,J=oe,O=X,P=q;return i(),y("div",null,[k("div",ne,[l(r,{modelValue:e.name,"onUpdate:modelValue":t[0]||(t[0]=o=>e.name=o),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入房间编号查询"},null,8,["modelValue"]),l(p,{type:"info",plain:"",onClick:m},{default:n(()=>t[8]||(t[8]=[d("查询")])),_:1,__:[8]}),l(p,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:U},{default:n(()=>t[9]||(t[9]=[d("重置")])),_:1,__:[9]})]),e.user.role==="BUSINESS"?(i(),y("div",re,[l(p,{type:"primary",plain:"",onClick:S},{default:n(()=>t[10]||(t[10]=[d("新增")])),_:1,__:[10]}),l(p,{type:"danger",plain:"",onClick:D},{default:n(()=>t[11]||(t[11]=[d("批量删除")])),_:1,__:[11]})])):c("",!0),k("div",se,[l(z,{stripe:"",data:e.tableData,onSelectionChange:I},{default:n(()=>[l(u,{type:"selection",width:"55"}),l(u,{prop:"businessName",label:"所属民宿"}),l(u,{prop:"typeName",label:"房间类型"}),l(u,{prop:"name",label:"房间编号"}),l(u,{prop:"level",label:"楼层"}),l(u,{prop:"status",label:"状态"},{default:n(o=>[o.row.status==="使用中"?(i(),_(b,{key:0,type:"danger"},{default:n(()=>[d(w(o.row.status),1)]),_:2},1024)):c("",!0),o.row.status==="空闲"?(i(),_(b,{key:1,type:"success"},{default:n(()=>[d(w(o.row.status),1)]),_:2},1024)):c("",!0),o.row.status==="正在维修"?(i(),_(b,{key:2,type:"warning"},{default:n(()=>[d(w(o.row.status),1)]),_:2},1024)):c("",!0)]),_:1}),l(u,{label:"操作",width:"100",fixed:"right"},{default:n(o=>[e.user.role==="BUSINESS"?(i(),_(p,{key:0,type:"primary",circle:"",icon:v(H),onClick:R=>x(o.row)},null,8,["icon","onClick"])):c("",!0),l(p,{type:"danger",circle:"",icon:v(W),onClick:R=>B(o.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),e.total?(i(),y("div",ie,[l(T,{onCurrentChange:m,background:"",layout:"prev, pager, next","page-size":e.pageSize,"current-page":e.pageNum,"onUpdate:currentPage":t[1]||(t[1]=o=>e.pageNum=o),total:e.total},null,8,["page-size","current-page","total"])])):c("",!0),l(P,{title:"房间信息",modelValue:e.formVisible,"onUpdate:modelValue":t[7]||(t[7]=o=>e.formVisible=o),width:"40%","destroy-on-close":""},{footer:n(()=>[k("span",de,[l(p,{onClick:t[6]||(t[6]=o=>e.formVisible=!1)},{default:n(()=>t[12]||(t[12]=[d("取 消")])),_:1,__:[12]}),l(p,{type:"primary",onClick:h},{default:n(()=>t[13]||(t[13]=[d("确 定")])),_:1,__:[13]})])]),default:n(()=>[l(O,{ref:"form",model:e.form,"label-width":"70px",style:{padding:"20px"}},{default:n(()=>[l(g,{prop:"typeId",label:"房间类型"},{default:n(()=>[l(F,{modelValue:e.form.typeId,"onUpdate:modelValue":t[2]||(t[2]=o=>e.form.typeId=o),placeholder:"请选择房间类型",style:{width:"100%"}},{default:n(()=>[(i(!0),y(ee,null,te(e.typeData,o=>(i(),_(A,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(g,{prop:"name",label:"房间编号"},{default:n(()=>[l(r,{modelValue:e.form.name,"onUpdate:modelValue":t[3]||(t[3]=o=>e.form.name=o),placeholder:"请输入房间编号"},null,8,["modelValue"])]),_:1}),l(g,{prop:"level",label:"房间楼层"},{default:n(()=>[l(r,{modelValue:e.form.level,"onUpdate:modelValue":t[4]||(t[4]=o=>e.form.level=o),placeholder:"请输入房间楼层"},null,8,["modelValue"])]),_:1}),l(g,{prop:"status",label:"宾馆状态"},{default:n(()=>[l(J,{modelValue:e.form.status,"onUpdate:modelValue":t[5]||(t[5]=o=>e.form.status=o),fill:"#A3A6AD"},{default:n(()=>[l(V,{label:"空闲",value:"空闲"}),l(V,{label:"使用中",value:"使用中"}),l(V,{label:"正在维修",value:"正在维修"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}};export{he as default};
