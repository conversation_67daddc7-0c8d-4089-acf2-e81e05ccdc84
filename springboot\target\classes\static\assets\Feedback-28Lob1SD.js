/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as f}from"./request-BNOh435t.js";/* empty css                    */import{r as T,c as w,a as _,n as u,b as o,z as U,w as a,Q as P,A as F,B as O,C as q,D as I,o as d,R as J,j as r,G as $,m as c,P as j,t as V,K as A,L as G,E as g}from"./index-DXyN40AS.js";const K={class:"card",style:{"margin-bottom":"5px"}},L={class:"card",style:{"margin-bottom":"5px"}},M={key:0,class:"card"},Q={class:"dialog-footer"},dt={__name:"Feedback",setup(R){const t=T({formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,title:null,status:null,ids:[]}),i=()=>{f.get("/feedback/selectPage",{params:{pageNum:t.pageNum,pageSize:t.pageSize,title:t.title,status:t.status}}).then(s=>{var e,p;s.code==="200"&&(t.tableData=((e=s.data)==null?void 0:e.list)||[],t.total=(p=s.data)==null?void 0:p.total)})},x=s=>{t.form=JSON.parse(JSON.stringify(s)),t.formVisible=!0},k=()=>{f.post("/feedback/add",t.form).then(s=>{s.code==="200"?(g.success("操作成功"),t.formVisible=!1,i()):g.error(s.msg)})},E=()=>{f.put("/feedback/update",t.form).then(s=>{s.code==="200"&&(g.success("操作成功"),t.formVisible=!1,i())})},v=()=>{t.form.id?E():k()},C=()=>{t.title=null,t.status=null,i()};return i(),(s,e)=>{const p=U,b=J,N=P,m=F,n=$,y=j,S=O,z=q,h=G,B=A,D=I;return d(),w("div",null,[_("div",K,[o(p,{modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=l=>t.title=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入反馈标题查询"},null,8,["modelValue"]),o(N,{modelValue:t.status,"onUpdate:modelValue":e[1]||(e[1]=l=>t.status=l),placeholder:"请选择回复状态查询",style:{width:"240px","margin-right":"10px"}},{default:a(()=>[o(b,{label:"等待回复",value:"等待回复"}),o(b,{label:"已回复",value:"已回复"})]),_:1},8,["modelValue"]),o(m,{type:"info",plain:"",onClick:i},{default:a(()=>e[6]||(e[6]=[r("查询")])),_:1,__:[6]}),o(m,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:C},{default:a(()=>e[7]||(e[7]=[r("重置")])),_:1,__:[7]})]),_("div",L,[o(S,{stripe:"",data:t.tableData},{default:a(()=>[o(n,{prop:"username",label:"反馈用户"}),o(n,{prop:"title",label:"标题","show-overflow-tooltip":""}),o(n,{prop:"question",label:"反馈问题","show-overflow-tooltip":""}),o(n,{prop:"idea",label:"用户建议","show-overflow-tooltip":""}),o(n,{prop:"time",label:"发布时间",width:"180px"}),o(n,{prop:"name",label:"回复人"}),o(n,{prop:"content",label:"回复内容"}),o(n,{prop:"replyTime",label:"回复时间",width:"180px"}),o(n,{prop:"status",label:"回复状态"},{default:a(l=>[l.row.status==="等待回复"?(d(),c(y,{key:0,type:"danger"},{default:a(()=>[r(V(l.row.status),1)]),_:2},1024)):u("",!0),l.row.status==="已回复"?(d(),c(y,{key:1,type:"success"},{default:a(()=>[r(V(l.row.status),1)]),_:2},1024)):u("",!0)]),_:1}),o(n,{label:"操作",width:"100",fixed:"right"},{default:a(l=>[l.row.status==="等待回复"?(d(),c(m,{key:0,type:"primary",onClick:H=>x(l.row)},{default:a(()=>e[8]||(e[8]=[r("立即回复")])),_:2,__:[8]},1032,["onClick"])):u("",!0)]),_:1})]),_:1},8,["data"])]),t.total?(d(),w("div",M,[o(z,{onCurrentChange:i,background:"",layout:"prev, pager, next","page-size":t.pageSize,"current-page":t.pageNum,"onUpdate:currentPage":e[2]||(e[2]=l=>t.pageNum=l),total:t.total},null,8,["page-size","current-page","total"])])):u("",!0),o(D,{title:"回复信息",modelValue:t.formVisible,"onUpdate:modelValue":e[5]||(e[5]=l=>t.formVisible=l),width:"40%","destroy-on-close":""},{footer:a(()=>[_("span",Q,[o(m,{onClick:e[4]||(e[4]=l=>t.formVisible=!1)},{default:a(()=>e[9]||(e[9]=[r("取 消")])),_:1,__:[9]}),o(m,{type:"primary",onClick:v},{default:a(()=>e[10]||(e[10]=[r("确 定")])),_:1,__:[10]})])]),default:a(()=>[o(B,{ref:"form",model:t.form,"label-width":"70px",style:{padding:"20px"}},{default:a(()=>[o(h,{prop:"content",label:"回复内容"},{default:a(()=>[o(p,{type:"textarea",rows:4,modelValue:t.form.content,"onUpdate:modelValue":e[3]||(e[3]=l=>t.form.content=l),placeholder:"请输入回复内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}};export{dt as default};
