/* empty css                *//* empty css            */import{r as f}from"./request-BNOh435t.js";import{r as k,c,b as n,a as t,w as r,$ as B,F as _,x,a0 as E,E as h,h as u,o as a,m,a1 as $,t as l,k as A,j as N,a2 as V}from"./index-DXyN40AS.js";const I={style:{"min-height":"600px"}},L=["src","onClick"],R={style:{width:"80%",display:"flex",margin:"20px auto","grid-gap":"40px"}},S={style:{width:"400px"}},F={style:{display:"flex","align-items":"center","border-bottom":"1px solid #ccc","padding-bottom":"10px"}},T={style:{"margin-top":"10px"}},j={class:"front_card",style:{"margin-bottom":"20px"}},q=["src","onClick"],H={style:{padding:"10px"}},M={style:{display:"flex","align-items":"center"}},G={style:{flex:"1","font-size":"18px","font-weight":"bold"}},J={style:{display:"flex",width:"70px","align-items":"center"}},K={style:{"font-size":"16px"}},O={style:{"margin-top":"10px",color:"red","font-size":"16px"}},P={style:{"font-weight":"bold","font-size":"22px"}},Q={style:{flex:"1"}},U={style:{display:"flex","align-items":"center","border-bottom":"1px solid #ccc","padding-bottom":"10px"}},W={style:{"margin-top":"10px"}},X=["src","onClick"],Y={style:{padding:"10px"}},Z={style:{display:"flex","align-items":"center"}},tt={class:"line1",style:{flex:"1","font-size":"18px","font-weight":"bold"}},et={style:{display:"flex",width:"70px","align-items":"center"}},st={style:{"font-size":"16px"}},ot={style:{"margin-top":"20px",display:"flex","align-items":"center","border-bottom":"1px solid #ccc","padding-bottom":"10px"}},it={style:{"margin-top":"10px"}},nt={class:"card",style:{"margin-bottom":"10px",display:"flex","grid-gap":"20px"}},lt=["src"],at={style:{"font-size":"18px"}},rt=["onClick"],dt={style:{"margin-top":"20px",display:"flex","align-items":"center"}},ct=["src"],pt={style:{"margin-left":"5px","margin-right":"20px",color:"#74726b"}},_t={style:{"margin-left":"5px","margin-right":"20px",color:"#74726b"}},xt={style:{"margin-left":"5px","margin-right":"20px"}},Dt={__name:"Home",setup(gt){const s=k({businessData:[],leftBusinessData:[],sceneryData:[],articleData:[]});(()=>{f.get("/business/selectAll",{params:{status:"通过"}}).then(o=>{o.code==="200"?(s.businessData=o.data,s.businessData.length>4?s.leftBusinessData=s.businessData.slice(0,3):s.leftBusinessData=s.businessData):h.error(o.msg)})})(),(()=>{f.get("/scenery/selectAll").then(o=>{o.code==="200"?(s.sceneryData=o.data,s.sceneryData.length>6&&(s.sceneryData=s.sceneryData.slice(0,6))):h.error(o.msg)})})(),(()=>{f.get("/article/selectAll").then(o=>{o.code==="200"?(s.articleData=o.data,s.articleData.length>2&&(s.articleData=s.articleData.slice(0,2))):h.error(o.msg)})})();const d=o=>{location.href=o};return(o,i)=>{const v=$,b=B,y=u("LocationInformation"),p=A,D=V,w=E,C=u("View"),z=u("ChatDotRound");return a(),c("div",I,[n(b,{interval:4e3,type:"card",height:"300px"},{default:r(()=>[(a(!0),c(_,null,x(s.businessData,e=>(a(),m(v,{key:e.id},{default:r(()=>[t("img",{src:e.img,alt:"",style:{width:"100%",height:"300px"},onClick:g=>d("/front/businessDetail?id="+e.id)},null,8,L)]),_:2},1024))),128))]),_:1}),t("div",R,[t("div",S,[t("div",F,[i[3]||(i[3]=t("div",{style:{flex:"1","font-size":"20px","font-weight":"bold",color:"#35ab3d"}},"相关宾馆",-1)),t("div",{style:{width:"120px",color:"#666","font-size":"15px","text-align":"right",cursor:"pointer"},onClick:i[0]||(i[0]=e=>d("/front/business"))},"查看更多宾馆 >")]),t("div",T,[(a(!0),c(_,null,x(s.leftBusinessData,e=>(a(),c("div",j,[t("img",{src:e.img,alt:"",style:{width:"100%",height:"220px","border-radius":"5px",cursor:"pointer"},onClick:g=>d("/front/businessDetail?id="+e.id)},null,8,q),t("div",H,[t("div",M,[t("div",G,l(e.name),1),t("div",J,[n(p,{size:"18"},{default:r(()=>[n(y)]),_:1}),t("div",K,l(e.provinceName),1)])]),t("div",O,[t("span",P,"￥"+l(e.price),1),i[4]||(i[4]=N(" 起 "))])])]))),256))])]),t("div",Q,[t("div",U,[i[5]||(i[5]=t("div",{style:{flex:"1","font-size":"20px","font-weight":"bold",color:"#35ab3d"}},"当地景点",-1)),t("div",{style:{width:"120px",color:"#666","font-size":"15px","text-align":"right",cursor:"pointer"},onClick:i[1]||(i[1]=e=>d("/front/scenery"))},"查看更多 >")]),t("div",W,[n(w,{gutter:20},{default:r(()=>[(a(!0),c(_,null,x(s.sceneryData,e=>(a(),m(D,{span:8,style:{"margin-bottom":"20px"},class:"front_card"},{default:r(()=>[t("img",{src:e.img,alt:"",style:{width:"100%",height:"220px","border-radius":"5px",cursor:"pointer"},onClick:g=>d("/front/sceneryDetail?id="+e.id)},null,8,X),t("div",Y,[t("div",Z,[t("div",tt,l(e.name),1),t("div",et,[n(p,{size:"18"},{default:r(()=>[n(y)]),_:1}),t("div",st,l(e.provinceName),1)])])])]),_:2},1024))),256))]),_:1})]),t("div",ot,[i[6]||(i[6]=t("div",{style:{flex:"1","font-size":"20px","font-weight":"bold",color:"#35ab3d"}},"旅游帖子",-1)),t("div",{style:{width:"120px",color:"#666","font-size":"15px","text-align":"right",cursor:"pointer"},onClick:i[2]||(i[2]=e=>d("/front/article"))},"查看更多 >")]),t("div",it,[(a(!0),c(_,null,x(s.articleData,e=>(a(),c("div",nt,[t("img",{src:e.img,alt:"",style:{width:"200px",height:"150px","border-radius":"5px"}},null,8,lt),t("div",null,[t("div",at,l(e.title),1),t("div",{onClick:g=>d("/front/articleDetail?id="+e.id),class:"line4",style:{color:"#74726b","margin-top":"10px",cursor:"pointer"}},l(e.content),9,rt),t("div",dt,[t("img",{src:e.userAvatar,alt:"",style:{width:"20px",height:"20px","border-radius":"50%"}},null,8,ct),t("div",pt,l(e.userName),1),n(p,{size:"15"},{default:r(()=>[n(C)]),_:1}),t("div",_t,l(e.views),1),n(p,{size:"15"},{default:r(()=>[n(z)]),_:1}),t("div",xt,l(e.comment),1)])])]))),256))])])])])}}};export{Dt as default};
