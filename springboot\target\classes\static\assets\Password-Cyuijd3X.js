/* empty css                *//* empty css                  *//* empty css               *//* empty css              */import{r as _}from"./request-BNOh435t.js";import{O as c,r as g,c as P,b as r,w as a,K as V,o as x,a as E,L as b,z as v,A as h,j as y,E as n,f as B}from"./index-DXyN40AS.js";const I={style:{width:"50%"},class:"card"},N={style:{"text-align":"center"}},R={__name:"Password",setup(q){const u=c(),m=(l,e,o)=>{e?(e!==s.user.newPassword&&o(new Error("确认密码跟原密码不一致!")),o()):o(new Error("请确认密码"))},s=g({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),rules:{password:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{validator:m,trigger:"blur"}]}}),p=()=>{u.value.validate(l=>{l&&_.put("/updatePassword",s.user).then(e=>{e.code==="200"?(n.success("保存成功"),i()):n.error(e.msg)})})},i=()=>{localStorage.removeItem("xm-user"),B.push("/login")};return(l,e)=>{const o=v,d=b,w=h,f=V;return x(),P("div",I,[r(f,{ref_key:"formRef",ref:u,rules:s.rules,model:s.user,"label-width":"80px",style:{padding:"20px"}},{default:a(()=>[r(d,{label:"原密码",prop:"password"},{default:a(()=>[r(o,{modelValue:s.user.password,"onUpdate:modelValue":e[0]||(e[0]=t=>s.user.password=t),placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),r(d,{label:"新密码",prop:"newPassword"},{default:a(()=>[r(o,{modelValue:s.user.newPassword,"onUpdate:modelValue":e[1]||(e[1]=t=>s.user.newPassword=t),placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),r(d,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[r(o,{modelValue:s.user.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=t=>s.user.confirmPassword=t),placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]),_:1}),E("div",N,[r(w,{type:"primary",onClick:p},{default:a(()=>e[3]||(e[3]=[y("保 存")])),_:1,__:[3]})])]),_:1},8,["rules","model"])])}}};export{R as default};
