/**
 * Take input from [0, n] and return it as [0, 1]
 * @hidden
 */
export declare function bound01(n: any, max: number): number;
/**
 * Force a number between 0 and 1
 * @hidden
 */
export declare function clamp01(val: number): number;
/**
 * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1
 * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>
 * @hidden
 */
export declare function isOnePointZero(n: string | number): boolean;
/**
 * Check to see if string passed in is a percentage
 * @hidden
 */
export declare function isPercentage(n: string | number): boolean;
/**
 * Return a valid alpha value [0,1] with all invalid values being set to 1
 * @hidden
 */
export declare function boundAlpha(a?: number | string): number;
/**
 * Replace a decimal with it's percentage value
 * @hidden
 */
export declare function convertToPercentage(n: number | string): number | string;
/**
 * Force a hex value to have 2 characters
 * @hidden
 */
export declare function pad2(c: string): string;
