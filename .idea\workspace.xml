<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="226e9ac3-ab75-4896-ab6c-8592cddf460d" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zEKtybNpjM8u2Y6gz5hobHPsZp" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.springboot [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.springboot [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.springboot [org.apache.maven.plugins:maven-clean-plugin:3.3.2:clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.springboot [org.apache.maven.plugins:maven-compiler-plugin:3.13.0:compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.springboot [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.SpringbootApplication.executor&quot;: &quot;Run&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/shujuku/hotelsystem/vue/src/assets/imgs&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.59195405&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ IDEA 2023.3.8\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\shujuku\hotelsystem\vue\src\assets\imgs" />
      <recent name="D:\shujuku\hotelsystem\vue\src\views\manager" />
      <recent name="D:\shujuku\hotelsystem\springboot\src\main\resources\mapper" />
      <recent name="D:\shujuku\hotelsystem\vue\public" />
      <recent name="D:\shujuku\hotelsystem\vue\src\views\front" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\shujuku\hotelsystem\springboot\src\main\resources\mapper" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.example.controller" />
      <recent name="com.example.service" />
      <recent name="com.example.mapper" />
      <recent name="com.example.entity" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.SpringbootApplication">
    <configuration name="SpringbootApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="springboot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.SpringbootApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/vue/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="$PROJECT_DIR$/../../nodejs/node" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.17011.79" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.17011.79" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="226e9ac3-ab75-4896-ab6c-8592cddf460d" name="更改" comment="" />
      <created>1751291010990</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751291010990</updated>
      <workItem from="1751291012069" duration="1425000" />
      <workItem from="1751292459825" duration="462000" />
      <workItem from="1751292940137" duration="1458000" />
      <workItem from="1751294427233" duration="253000" />
      <workItem from="1751294700066" duration="30000" />
      <workItem from="1751294960037" duration="62000" />
      <workItem from="1751295036344" duration="6000" />
      <workItem from="1751295053885" duration="35000" />
      <workItem from="1751296004552" duration="242000" />
      <workItem from="1751332643897" duration="1459000" />
      <workItem from="1751334128545" duration="288000" />
      <workItem from="1751334889160" duration="654000" />
      <workItem from="1751336110150" duration="1306000" />
      <workItem from="1751337433260" duration="40000" />
      <workItem from="1751337480544" duration="478000" />
      <workItem from="1751337975186" duration="873000" />
      <workItem from="1751338861881" duration="116000" />
      <workItem from="1751339497831" duration="203000" />
      <workItem from="1751340373520" duration="364000" />
      <workItem from="1751341081427" duration="425000" />
      <workItem from="1751341515322" duration="35000" />
      <workItem from="1751343636177" duration="656000" />
      <workItem from="1751347776485" duration="40000" />
      <workItem from="1751366054046" duration="67000" />
      <workItem from="1751375265890" duration="577000" />
      <workItem from="1751376051537" duration="382000" />
      <workItem from="1751384260624" duration="42000" />
      <workItem from="1751384670373" duration="228000" />
      <workItem from="1751384916337" duration="220000" />
      <workItem from="1751419216590" duration="3576000" />
      <workItem from="1751423850421" duration="249000" />
      <workItem from="1751441205669" duration="79000" />
      <workItem from="1751442799787" duration="508000" />
      <workItem from="1751466152527" duration="461000" />
      <workItem from="1751471478990" duration="78000" />
      <workItem from="1751508529786" duration="120000" />
      <workItem from="1751513954103" duration="247000" />
      <workItem from="1751522796740" duration="81000" />
      <workItem from="1751596996283" duration="5285000" />
      <workItem from="1751602357721" duration="2428000" />
      <workItem from="1751604900064" duration="68000" />
      <workItem from="1751617795213" duration="7405000" />
      <workItem from="1751627088919" duration="124000" />
      <workItem from="1751683949147" duration="3865000" />
      <workItem from="1751688036802" duration="744000" />
      <workItem from="1751688954908" duration="224000" />
      <workItem from="1751689248007" duration="189000" />
      <workItem from="1751690368410" duration="293000" />
      <workItem from="1751707925355" duration="3554000" />
      <workItem from="1751711707583" duration="163000" />
      <workItem from="1751721047968" duration="278000" />
      <workItem from="1751726930908" duration="127000" />
      <workItem from="1751811463753" duration="125000" />
      <workItem from="1751811651432" duration="93000" />
      <workItem from="1751854733837" duration="2842000" />
      <workItem from="1751859446148" duration="1873000" />
      <workItem from="1751879383260" duration="652000" />
      <workItem from="1751882234186" duration="61000" />
      <workItem from="1751890868201" duration="59000" />
      <workItem from="1751961155624" duration="4105000" />
      <workItem from="1751965351278" duration="98000" />
      <workItem from="1751980062017" duration="5276000" />
      <workItem from="1751985567926" duration="81000" />
      <workItem from="1752033479799" duration="401000" />
      <workItem from="1752033926103" duration="188000" />
      <workItem from="1752064881175" duration="4233000" />
      <workItem from="1752134250496" duration="6312000" />
      <workItem from="1752140622029" duration="487000" />
      <workItem from="1752311586300" duration="4934000" />
      <workItem from="1752543413379" duration="7245000" />
      <workItem from="1752552784568" duration="152000" />
      <workItem from="1752552947286" duration="74000" />
      <workItem from="1752571073128" duration="3194000" />
      <workItem from="1752575184857" duration="220000" />
      <workItem from="1752628448658" duration="2652000" />
      <workItem from="1752631132061" duration="4966000" />
      <workItem from="1752654563451" duration="4331000" />
      <workItem from="1752712762242" duration="5828000" />
      <workItem from="1752739525802" duration="5895000" />
      <workItem from="1752764098870" duration="281000" />
      <workItem from="1752814400715" duration="7070000" />
      <workItem from="1752827111243" duration="391000" />
      <workItem from="1752827993591" duration="130000" />
      <workItem from="1752828389787" duration="204000" />
      <workItem from="1752848337280" duration="90000" />
      <workItem from="1752890831978" duration="7542000" />
      <workItem from="1752915186743" duration="4771000" />
      <workItem from="1752922677804" duration="218000" />
      <workItem from="1752923009990" duration="396000" />
      <workItem from="1752999827381" duration="222000" />
      <workItem from="1753000151035" duration="3786000" />
      <workItem from="1753003962365" duration="760000" />
      <workItem from="1753004965324" duration="618000" />
      <workItem from="1753014251760" duration="386000" />
      <workItem from="1753017714365" duration="78000" />
      <workItem from="1753019062098" duration="3592000" />
      <workItem from="1753083775278" duration="5409000" />
      <workItem from="1753090150060" duration="2221000" />
      <workItem from="1753092450861" duration="66000" />
      <workItem from="1753101723776" duration="6804000" />
      <workItem from="1753152298699" duration="4667000" />
      <workItem from="1753169155048" duration="11104000" />
      <workItem from="1753191116239" duration="260000" />
      <workItem from="1753191848738" duration="259000" />
      <workItem from="1753236900629" duration="1081000" />
      <workItem from="1753254609029" duration="5895000" />
      <workItem from="1753267909649" duration="5793000" />
      <workItem from="1753273971232" duration="100000" />
      <workItem from="1753326677009" duration="3231000" />
      <workItem from="1753341663645" duration="6119000" />
      <workItem from="1753359219308" duration="68000" />
      <workItem from="1753411468659" duration="5445000" />
      <workItem from="1753432072460" duration="4230000" />
      <workItem from="1753436417596" duration="240000" />
      <workItem from="1753498347711" duration="920000" />
      <workItem from="1753500155070" duration="55000" />
      <workItem from="1753670289045" duration="6310000" />
      <workItem from="1753689417199" duration="2893000" />
      <workItem from="1753694579093" duration="2097000" />
      <workItem from="1753696829137" duration="60000" />
      <workItem from="1753754411813" duration="6447000" />
      <workItem from="1753774077636" duration="3529000" />
      <workItem from="1753777628469" duration="3079000" />
      <workItem from="1753781212271" duration="65000" />
      <workItem from="1753797406776" duration="72000" />
      <workItem from="1753839755022" duration="5706000" />
      <workItem from="1753855257037" duration="278000" />
      <workItem from="1753860910627" duration="6135000" />
      <workItem from="1753881386953" duration="3824000" />
      <workItem from="1753927254547" duration="5609000" />
      <workItem from="1753945180159" duration="8018000" />
      <workItem from="1753955466609" duration="56000" />
      <workItem from="1753960165488" duration="7453000" />
      <workItem from="1753967903277" duration="193000" />
      <workItem from="1754035303815" duration="540000" />
      <workItem from="1754036459982" duration="576000" />
      <workItem from="1754039127622" duration="1331000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>