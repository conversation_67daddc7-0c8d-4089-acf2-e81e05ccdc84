/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                     *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css                     *//* empty css               *//* empty css              */import{r as f}from"./request-BNOh435t.js";/* empty css                    */import{O as P,r as R,c as k,a as c,n as _,b as o,z as $,w as a,A as H,B as L,C as F,D as G,o as m,j as r,G as j,m as g,H as q,P as K,t as b,u as Q,J as W,K as X,L as Y,T as Z,U as ee,N as C,E as u}from"./index-DXyN40AS.js";import"./index.esm-CA4zuM-h.js";const te={class:"card",style:{"margin-bottom":"5px"}},oe={class:"card",style:{"margin-bottom":"5px"}},le={class:"card",style:{"margin-bottom":"5px"}},ae={key:0,class:"card"},ie={class:"dialog-footer"},re=["innerHTML"],Ne={__name:"Article",setup(ne){const x=P(),t=R({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),formVisible:!1,form:{},tableData:[],provinceData:[],pageNum:1,pageSize:5,total:0,name:null,ids:[],viewContent:null,viewVisible:null}),p=()=>{f.get("/article/selectPage",{params:{pageNum:t.pageNum,pageSize:t.pageSize,title:t.title}}).then(i=>{var e,d;i.code==="200"&&(t.tableData=((e=i.data)==null?void 0:e.list)||[],t.total=(d=i.data)==null?void 0:d.total)})},E=i=>{t.form=JSON.parse(JSON.stringify(i)),t.formVisible=!0},h=i=>{t.viewContent=i,t.viewVisible=!0},N=()=>{f.put("/article/update",t.form).then(i=>{i.code==="200"&&(u.success("操作成功"),t.formVisible=!1,p())})},S=()=>{N()},B=i=>{C.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(e=>{f.delete("/article/delete/"+i).then(d=>{d.code==="200"?(u.success("删除成功"),p()):u.error(d.msg)})}).catch(e=>{console.error(e)})},D=()=>{if(!t.ids.length){u.warning("请选择数据");return}C.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(i=>{f.delete("/article/delete/batch",{data:t.ids}).then(e=>{e.code==="200"?(u.success("操作成功"),p()):u.error(e.msg)})}).catch(i=>{console.error(i)})},z=i=>{t.ids=i.map(e=>e.id)},T=()=>{t.title=null,p()};return p(),(i,e)=>{const d=$,s=H,n=j,U=q,w=K,A=L,I=F,y=ee,J=Z,M=Y,O=X,v=G;return m(),k("div",null,[c("div",te,[o(d,{modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=l=>t.title=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入帖子名称查询"},null,8,["modelValue"]),o(s,{type:"info",plain:"",onClick:p},{default:a(()=>e[6]||(e[6]=[r("查询")])),_:1,__:[6]}),o(s,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:T},{default:a(()=>e[7]||(e[7]=[r("重置")])),_:1,__:[7]})]),c("div",oe,[o(s,{type:"danger",plain:"",onClick:D},{default:a(()=>e[8]||(e[8]=[r("批量删除")])),_:1,__:[8]})]),c("div",le,[o(A,{stripe:"",data:t.tableData,onSelectionChange:z,"tooltip-effect":"light myEffect"},{default:a(()=>[o(n,{type:"selection",width:"55"}),o(n,{prop:"img",label:"帖子封面",width:"80"},{default:a(l=>[l.row.img?(m(),g(U,{key:0,style:{width:"40px",height:"40px","border-radius":"5px",display:"block"},src:l.row.img,"preview-src-list":[l.row.img],"preview-teleported":""},null,8,["src","preview-src-list"])):_("",!0)]),_:1}),o(n,{prop:"title",label:"帖子标题","show-overflow-tooltip":""}),o(n,{prop:"content",label:"帖子内容"},{default:a(l=>[o(s,{type:"primary",onClick:V=>h(l.row.content)},{default:a(()=>e[9]||(e[9]=[r("查看详情")])),_:2,__:[9]},1032,["onClick"])]),_:1}),o(n,{prop:"userName",label:"发布人"}),o(n,{prop:"time",label:"发布时间"}),o(n,{prop:"views",label:"浏览量"}),o(n,{prop:"status",label:"帖子状态"},{default:a(l=>[l.row.status==="待审核"?(m(),g(w,{key:0,type:"warning"},{default:a(()=>[r(b(l.row.status),1)]),_:2},1024)):_("",!0),l.row.status==="通过"?(m(),g(w,{key:1,type:"success"},{default:a(()=>[r(b(l.row.status),1)]),_:2},1024)):_("",!0),l.row.status==="拒绝"?(m(),g(w,{key:2,type:"danger"},{default:a(()=>[r(b(l.row.status),1)]),_:2},1024)):_("",!0)]),_:1}),o(n,{label:"操作",width:"150",fixed:"right"},{default:a(l=>[o(s,{type:"primary",onClick:V=>E(l.row)},{default:a(()=>e[10]||(e[10]=[r("审批")])),_:2,__:[10]},1032,["onClick"]),o(s,{type:"danger",circle:"",icon:Q(W),onClick:V=>B(l.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),t.total?(m(),k("div",ae,[o(I,{onCurrentChange:p,background:"",layout:"prev, pager, next","page-size":t.pageSize,"current-page":t.pageNum,"onUpdate:currentPage":e[1]||(e[1]=l=>t.pageNum=l),total:t.total},null,8,["page-size","current-page","total"])])):_("",!0),o(v,{title:"审核信息",modelValue:t.formVisible,"onUpdate:modelValue":e[4]||(e[4]=l=>t.formVisible=l),width:"50%","destroy-on-close":""},{footer:a(()=>[c("span",ie,[o(s,{onClick:e[3]||(e[3]=l=>t.formVisible=!1)},{default:a(()=>e[11]||(e[11]=[r("取 消")])),_:1,__:[11]}),o(s,{type:"primary",onClick:S},{default:a(()=>e[12]||(e[12]=[r("确 定")])),_:1,__:[12]})])]),default:a(()=>[o(O,{ref_key:"formRef",ref:x,model:t.form,rules:t.rules,"label-width":"100px",style:{padding:"20px"}},{default:a(()=>[o(M,{prop:"status",label:"帖子状态"},{default:a(()=>[o(J,{modelValue:t.form.status,"onUpdate:modelValue":e[2]||(e[2]=l=>t.form.status=l),fill:"#A3A6AD"},{default:a(()=>[o(y,{label:"通过",value:"通过"}),o(y,{label:"拒绝",value:"拒绝"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),o(v,{title:"详细介绍",modelValue:t.viewVisible,"onUpdate:modelValue":e[5]||(e[5]=l=>t.viewVisible=l),width:"50%","destroy-on-close":""},{default:a(()=>[c("div",{innerHTML:t.viewContent,style:{padding:"20px"}},null,8,re)]),_:1},8,["modelValue"])])}}};export{Ne as default};
