/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                    *//* empty css              *//* empty css                  *//* empty css               */import{r as p}from"./request-BNOh435t.js";import{r as q,f as F,O as L,c as m,a as s,b as i,F as C,x as V,n as c,w as a,k as O,t as d,j as g,m as T,A as U,a8 as z,D as R,E as r,h as S,o as n,K as H,L as j,Y as J}from"./index-DXyN40AS.js";const K={style:{"min-height":"600px"}},P=["src"],G={style:{width:"90%",margin:"20px auto",display:"flex","grid-gap":"20px"}},Q={style:{width:"400px",padding:"0 20px"}},W={style:{"margin-top":"20px",padding:"0 20px"}},X={class:"front_card"},Z=["src","onClick"],ee={style:{"line-height":"40px",height:"40px","text-align":"center"}},te={style:{flex:"1",padding:"20px"}},se={style:{display:"flex","grid-gap":"40px"}},le={style:{flex:"3"}},oe=["src"],ie={style:{"margin-top":"5px",display:"flex","grid-gap":"10px"}},ae=["src"],ne=["src"],de={style:{flex:"2"}},re={style:{display:"flex","align-items":"center","font-size":"16px","grid-gap":"5px","margin-top":"10px"}},pe={style:{display:"flex","align-items":"center","font-size":"16px","grid-gap":"5px","margin-top":"10px"}},me={style:{display:"flex","align-items":"center","font-size":"16px","grid-gap":"5px","margin-top":"10px"}},ge={style:{color:"red","font-size":"16px","margin-top":"20px"}},ue={style:{"font-size":"20px","font-weight":"bold"}},ce={style:{"margin-top":"20px"}},fe={style:{"margin-top":"30px",display:"flex","grid-gap":"10px"}},xe=["innerHTML"],ye={key:1,style:{"margin-top":"35px"}},_e={style:{display:"flex","grid-gap":"20px","margin-bottom":"30px"}},he={style:{display:"flex","grid-gap":"5px"}},ve=["src"],be={style:{"margin-top":"10px",color:"#999"}},De={class:"dialog-footer"},Ae={__name:"TypeDetail",setup(ke){const e=q({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),img:null,typeId:F.currentRoute.value.query.id,businessData:{},typeData:{},extraTypeData:[],commentData:[],flag1:!0,flag2:!1,flag:!1,formVisible:!1,rules:{start:[{required:!0,message:"请选择入住日期",trigger:"blur"}],end:[{required:!0,message:"请选择离开日期",trigger:"blur"}]},form:{}}),_=l=>l.getTime()<Date.now(),E=()=>{e.form={},e.formVisible=!0},h=L(),Y=()=>{h.value.validate(l=>{l&&(e.form.typeId=e.typeId,e.form.businessId=e.businessData.id,p.post("businessOrders/add",e.form).then(t=>{t.code==="200"?(r.success("预订成功,请支付订单"),e.formVisible=!1):r.error(t.msg)}))})},v=()=>{p.get("/type/selectById/"+e.typeId).then(l=>{l.code==="200"?(e.typeData=l.data,e.img=e.typeData.img,p.get("/type/selectData/"+e.typeId).then(t=>{t.code==="200"?(e.businessData=t.data.businessData,e.extraTypeData=t.data.extraTypeData):r.error(t.msg)})):r.error(l.msg)})};v();const b=l=>{l==="info"&&(e.flag1=!0,e.flag2=!1),l==="comment"&&(e.flag1=!1,e.flag2=!0)},D=l=>{e.img=l},B=l=>{e.typeId=l,v(),k()},M=()=>{p.post("/collect/add",{typeId:e.typeId}).then(l=>{l.code==="200"&&(r.success("收藏成功"),f())})},$=()=>{p.get("/collect/deleteByUserId",{params:{userId:e.user.id,typeId:e.typeId}}).then(l=>{l.code==="200"?(r.success("取消成功"),f()):r.error(l.msg)})},f=()=>{p.get("collect/selectAll",{params:{userId:e.user.id,typeId:e.typeId}}).then(l=>{l.code==="200"&&(l.data.length>0?e.flag=!0:e.flag=!1)})};f();const k=()=>{p.get("businessOrders/selectAll",{params:{typeId:e.typeId}}).then(l=>{l.code==="200"?e.commentData=l.data.filter(t=>t.comment!==null):r.error(l.msg)})};return k(),(l,t)=>{const x=S("CircleCheck"),y=O,u=U,I=J,w=j,A=H,N=R;return n(),m("div",K,[s("img",{src:e.businessData.img,alt:"",style:{width:"100%",height:"550px"}},null,8,P),s("div",G,[s("div",Q,[t[9]||(t[9]=s("div",{style:{height:"40px","line-height":"40px",color:"#666","font-size":"20px","font-weight":"bold","text-align":"center","border-bottom":"1px solid #cccc"}},"其他类型房间",-1)),(n(!0),m(C,null,V(e.extraTypeData,o=>(n(),m("div",W,[s("div",X,[s("img",{src:o.img,alt:"",style:{width:"100%",height:"200px","border-radius":"5px 5px 0 0",cursor:"pointer"},onClick:Ie=>B(o.id)},null,8,Z),s("div",ee,d(o.name),1)])]))),256))]),s("div",te,[s("div",se,[s("div",le,[s("img",{src:e.img,alt:"",style:{width:"100%",height:"350px","margin-top":"20px"}},null,8,oe),s("div",ie,[s("img",{onClick:t[0]||(t[0]=o=>D(e.typeData.img1)),src:e.typeData.img1,alt:"",style:{width:"100px",height:"100px",cursor:"pointer"}},null,8,ae),s("img",{onClick:t[1]||(t[1]=o=>D(e.typeData.img2)),src:e.typeData.img2,alt:"",style:{width:"100px",height:"100px",cursor:"pointer"}},null,8,ne)])]),s("div",de,[t[15]||(t[15]=s("div",{style:{"font-size":"18px","font-weight":"bold"}},"基本信息",-1)),s("div",re,[i(y,null,{default:a(()=>[i(x)]),_:1}),s("div",null,"类型："+d(e.typeData.name),1)]),s("div",pe,[i(y,null,{default:a(()=>[i(x)]),_:1}),s("div",null,"剩余间数："+d(e.typeData.num),1)]),s("div",me,[i(y,null,{default:a(()=>[i(x)]),_:1}),s("div",null,"所属宾馆："+d(e.businessData.name),1)]),s("div",ge,[t[10]||(t[10]=g(" 价格：")),s("span",ue,d(e.typeData.price),1),t[11]||(t[11]=g(" /晚 "))]),s("div",ce,[e.flag?c("",!0):(n(),T(u,{key:0,type:"warning",onClick:M},{default:a(()=>t[12]||(t[12]=[g("收藏")])),_:1,__:[12]})),e.flag?(n(),T(u,{key:1,type:"danger",onClick:$},{default:a(()=>t[13]||(t[13]=[g("取消收藏")])),_:1,__:[13]})):c("",!0),i(u,{type:"success",onClick:t[2]||(t[2]=o=>E()),disabled:e.typeData.num===0},{default:a(()=>t[14]||(t[14]=[g("预订")])),_:1,__:[14]},8,["disabled"])])])]),s("div",fe,[s("div",{class:z({clickActive:e.flag1}),onClick:t[3]||(t[3]=o=>b("info")),style:{height:"40px",cursor:"pointer",width:"120px","line-height":"40px","box-shadow":"0 0 10px #bdbdbd","text-align":"center",border:"1px solid #e7e7e7","font-weight":"bold","font-size":"16px"}},"房间详情",2),s("div",{class:z({clickActive:e.flag2}),onClick:t[4]||(t[4]=o=>b("comment")),style:{height:"40px",cursor:"pointer",width:"120px","line-height":"40px","box-shadow":"0 0 10px #bdbdbd","text-align":"center",border:"1px solid #e7e7e7","font-weight":"bold","font-size":"16px"}},"入住评论",2)]),e.flag1?(n(),m("div",{key:0,style:{"margin-top":"35px"},innerHTML:e.typeData.content},null,8,xe)):c("",!0),e.flag2?(n(),m("div",ye,[(n(!0),m(C,null,V(e.commentData,o=>(n(),m("div",_e,[s("div",he,[s("img",{src:o.userAvatar,alt:"",style:{width:"40px",height:"40px","border-radius":"50%"}},null,8,ve),s("div",null,d(o.userName),1)]),s("div",null,[s("div",null,d(o.comment),1),s("div",be,d(o.commentTime),1)])]))),256))])):c("",!0)])]),i(N,{title:"预定房型信息",modelValue:e.formVisible,"onUpdate:modelValue":t[8]||(t[8]=o=>e.formVisible=o),width:"40%","destroy-on-close":""},{footer:a(()=>[s("span",De,[i(u,{onClick:t[7]||(t[7]=o=>e.formVisible=!1)},{default:a(()=>t[16]||(t[16]=[g("取 消")])),_:1,__:[16]}),i(u,{type:"primary",onClick:Y},{default:a(()=>t[17]||(t[17]=[g("预 订")])),_:1,__:[17]})])]),default:a(()=>[i(A,{ref_key:"formRef",ref:h,rules:e.rules,model:e.form,"label-width":"90px",style:{padding:"20px"}},{default:a(()=>[i(w,{prop:"start",label:"入住时间"},{default:a(()=>[i(I,{modelValue:e.form.start,"onUpdate:modelValue":t[5]||(t[5]=o=>e.form.start=o),type:"date",placeholder:"请选择入住日期","value-format":"YYYY-MM-DD","disabled-date":_},null,8,["modelValue"])]),_:1}),i(w,{prop:"end",label:"离开时间"},{default:a(()=>[i(I,{modelValue:e.form.end,"onUpdate:modelValue":t[6]||(t[6]=o=>e.form.end=o),type:"date",placeholder:"请选择离开日期","value-format":"YYYY-MM-DD","disabled-date":_},null,8,["modelValue"])]),_:1})]),_:1},8,["rules","model"])]),_:1},8,["modelValue"])])}}};export{Ae as default};
