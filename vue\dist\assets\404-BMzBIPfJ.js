import{_ as n,c as i,a as e,j as s,b as r,w as l,h as a,o as _}from"./index-DXyN40AS.js";const d="/assets/404-BE1X1IZM.jpg",c={},f={style:{height:"100vh",display:"flex","justify-content":"center","align-items":"center"}},p={style:{width:"30%"}},m={style:{"font-size":"30px","text-align":"center"}};function u(x,t){const o=a("router-link");return _(),i("div",null,[e("div",f,[e("div",p,[t[2]||(t[2]=e("img",{src:d,alt:"",style:{width:"100%"}},null,-1)),e("div",m,[t[1]||(t[1]=s("找不到页面啦！")),r(o,{to:"/"},{default:l(()=>t[0]||(t[0]=[s("请返回主页")])),_:1,__:[0]})])])])])}const y=n(c,[["render",u]]);export{y as default};
