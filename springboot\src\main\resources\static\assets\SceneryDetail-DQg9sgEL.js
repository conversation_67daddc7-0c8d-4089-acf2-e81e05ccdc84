/* empty css                *//* empty css             *//* empty css            */import{r as i}from"./request-BNOh435t.js";import{r as h,f as v,c as r,a as t,t as o,j as c,b as p,w as f,P as D,X as b,F as w,x as V,E as d,o as l}from"./index-DXyN40AS.js";const z={style:{width:"80%",margin:"20px auto","min-height":"600px"}},B={style:{display:"flex","grid-gap":"10px"}},E={style:{flex:"1"}},I={class:"card",style:{display:"flex","grid-gap":"20px"}},T=["src"],k={style:{"font-size":"18px"}},N={style:{"margin-top":"15px","font-size":"15px",color:"#666"}},C={style:{"margin-top":"15px","font-size":"15px",color:"#666",display:"flex","align-items":"center"}},L={style:{"margin-top":"20px","font-size":"15px","line-weight":"30px"}},M=["innerHTML"],S={style:{width:"400px"}},q={class:"card",style:{"font-size":"20px","font-weight":"bold"}},F={style:{"margin-top":"10px"}},H={class:"card"},R=["src","onClick"],j={style:{"font-size":"16px","margin-top":"10px"}},K={__name:"SceneryDetail",setup(A){const e=h({sceneryId:v.currentRoute.value.query.id,sceneryData:{},businessData:[]}),y=()=>{i.get("/scenery/selectById/"+e.sceneryId).then(s=>{s.code==="200"?(e.sceneryData=s.data,m(e.sceneryData.provinceId),_()):d.error(s.msg)})},_=()=>{e.sceneryData.views=e.sceneryData.views+1,i.put("/scenery/update",e.sceneryData).then(s=>{s.code!=="200"&&d.error(s.msg)})};y();const m=s=>{i.get("/business/selectAll",{params:{provinceId:s}}).then(n=>{n.code==="200"?e.businessData=n.data:d.error(n.msg)})},u=s=>{location.href=s};return(s,n)=>{const g=D,x=b;return l(),r("div",z,[t("div",B,[t("div",E,[t("div",I,[t("img",{src:e.sceneryData.img,alt:"",style:{height:"240px",width:"300px","border-radius":"5px"}},null,8,T),t("div",null,[t("div",k,o(e.sceneryData.name),1),t("div",N,[n[1]||(n[1]=c("所属省份；")),p(g,{type:"info"},{default:f(()=>[c(o(e.sceneryData.provinceName),1)]),_:1})]),t("div",C,[n[2]||(n[2]=t("div",null,"推荐指数；",-1)),p(x,{modelValue:e.sceneryData.recommend,"onUpdate:modelValue":n[0]||(n[0]=a=>e.sceneryData.recommend=a),disabled:""},null,8,["modelValue"])]),t("div",L," 简介；"+o(e.sceneryData.description),1)])]),t("div",{class:"card",style:{"margin-top":"10px",padding:"0 20px"},innerHTML:e.sceneryData.content},null,8,M)]),t("div",S,[t("div",q,"景点附近宾馆 ("+o(e.businessData.length)+")",1),t("div",F,[(l(!0),r(w,null,V(e.businessData,a=>(l(),r("div",H,[t("img",{src:a.img,alt:"",style:{width:"100%",height:"200px","border-radius":"5px",cursor:"pointer"},onClick:P=>u("/front/businessDetail?id="+a.id)},null,8,R),t("div",j,o(a.name),1)]))),256))])])])])}}};export{K as default};
