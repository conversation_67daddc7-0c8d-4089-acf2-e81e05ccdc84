/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                   *//* empty css            *//* empty css               *//* empty css                  *//* empty css              *//* empty css                     *//* empty css                *//* empty css               */import{r as u}from"./request-BNOh435t.js";/* empty css                    */import{r as F,c as w,a as c,n as _,b as o,t as g,z as M,w as s,Q as q,A as I,B as $,C as j,D as A,o as f,R as G,j as d,G as J,m as h,P as K,u as L,J as O,K as Q,L as R,N as x,E as p}from"./index-DXyN40AS.js";const H={style:{margin:"20px auto",width:"80%","min-height":"600px"}},W={style:{color:"darkorchid","font-size":"18px","margin-top":"20px"}},X={class:"card",style:{"margin-bottom":"5px"}},Y={class:"card",style:{"margin-bottom":"5px"}},Z={class:"card",style:{"margin-bottom":"5px"}},ee={key:0,class:"card"},te={class:"dialog-footer"},we={__name:"MyFeedback",setup(oe){const e=F({formVisible:!1,form:{},tableData:[],pageNum:1,pageSize:10,total:0,title:null,status:null,ids:[]}),i=()=>{u.get("/feedback/selectPage",{params:{pageNum:e.pageNum,pageSize:e.pageSize,title:e.title,status:e.status}}).then(a=>{var t,r;a.code==="200"&&(e.tableData=((t=a.data)==null?void 0:t.list)||[],e.total=(r=a.data)==null?void 0:r.total)})},V=()=>{if(!e.ids.length){p.warning("请选择数据");return}x.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(a=>{u.delete("/feedback/delete/batch",{data:e.ids}).then(t=>{t.code==="200"?(p.success("操作成功"),i()):p.error(t.msg)})}).catch(a=>{console.error(a)})},k=()=>{u.post("/feedback/add",e.form).then(a=>{a.code==="200"?(p.success("操作成功"),e.formVisible=!1,i()):p.error(a.msg)})},v=()=>{u.put("/feedback/update",e.form).then(a=>{a.code==="200"&&(p.success("操作成功"),e.formVisible=!1,i())})},C=()=>{e.form.id?v():k()},E=a=>{e.ids=a.map(t=>t.id)},N=a=>{x.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(t=>{u.delete("/feedback/delete/"+a).then(r=>{r.code==="200"?(p.success("删除成功"),i()):p.error(r.msg)})}).catch(t=>{console.error(t)})},S=()=>{e.title=null,e.status=null,i()};return i(),(a,t)=>{const r=M,b=G,z=q,m=I,n=J,y=K,B=$,D=j,T=R,U=Q,P=A;return f(),w("div",H,[c("div",W,"反馈记录 （"+g(e.total)+"）",1),c("div",X,[o(r,{modelValue:e.title,"onUpdate:modelValue":t[0]||(t[0]=l=>e.title=l),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入反馈标题查询"},null,8,["modelValue"]),o(z,{modelValue:e.status,"onUpdate:modelValue":t[1]||(t[1]=l=>e.status=l),placeholder:"请选择回复状态查询",style:{width:"240px","margin-right":"10px"}},{default:s(()=>[o(b,{label:"等待回复",value:"等待回复"}),o(b,{label:"已回复",value:"已回复"})]),_:1},8,["modelValue"]),o(m,{type:"info",plain:"",onClick:i},{default:s(()=>t[6]||(t[6]=[d("查询")])),_:1,__:[6]}),o(m,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:S},{default:s(()=>t[7]||(t[7]=[d("重置")])),_:1,__:[7]})]),c("div",Y,[o(m,{type:"danger",plain:"",onClick:V},{default:s(()=>t[8]||(t[8]=[d("批量删除")])),_:1,__:[8]})]),c("div",Z,[o(B,{stripe:"",data:e.tableData,onSelectionChange:E},{default:s(()=>[o(n,{type:"selection",width:"55"}),o(n,{prop:"title",label:"标题","show-overflow-tooltip":""}),o(n,{prop:"question",label:"反馈问题","show-overflow-tooltip":""}),o(n,{prop:"idea",label:"用户建议","show-overflow-tooltip":""}),o(n,{prop:"time",label:"发布时间",width:"180px"}),o(n,{prop:"name",label:"回复人"}),o(n,{prop:"content",label:"回复内容"}),o(n,{prop:"replyTime",label:"回复时间",width:"180px"}),o(n,{prop:"status",label:"回复状态"},{default:s(l=>[l.row.status==="等待回复"?(f(),h(y,{key:0,type:"danger"},{default:s(()=>[d(g(l.row.status),1)]),_:2},1024)):_("",!0),l.row.status==="已回复"?(f(),h(y,{key:1,type:"success"},{default:s(()=>[d(g(l.row.status),1)]),_:2},1024)):_("",!0)]),_:1}),o(n,{label:"操作",width:"100",fixed:"right"},{default:s(l=>[o(m,{type:"danger",circle:"",icon:L(O),onClick:le=>N(l.row.id)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),e.total?(f(),w("div",ee,[o(D,{onCurrentChange:i,background:"",layout:"prev, pager, next","page-size":e.pageSize,"current-page":e.pageNum,"onUpdate:currentPage":t[2]||(t[2]=l=>e.pageNum=l),total:e.total},null,8,["page-size","current-page","total"])])):_("",!0),o(P,{title:"回复信息",modelValue:e.formVisible,"onUpdate:modelValue":t[5]||(t[5]=l=>e.formVisible=l),width:"40%","destroy-on-close":""},{footer:s(()=>[c("span",te,[o(m,{onClick:t[4]||(t[4]=l=>e.formVisible=!1)},{default:s(()=>t[9]||(t[9]=[d("取 消")])),_:1,__:[9]}),o(m,{type:"primary",onClick:C},{default:s(()=>t[10]||(t[10]=[d("确 定")])),_:1,__:[10]})])]),default:s(()=>[o(U,{ref:"form",model:e.form,"label-width":"70px",style:{padding:"20px"}},{default:s(()=>[o(T,{prop:"content",label:"回复内容"},{default:s(()=>[o(r,{type:"textarea",rows:4,modelValue:e.form.content,"onUpdate:modelValue":t[3]||(t[3]=l=>e.form.content=l),placeholder:"请输入回复内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}};export{we as default};
