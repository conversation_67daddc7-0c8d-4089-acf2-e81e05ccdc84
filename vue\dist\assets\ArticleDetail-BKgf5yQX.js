/* empty css                *//* empty css               *//* empty css              */import{r}from"./request-BNOh435t.js";/* empty css                    *//* empty css                */import{r as D,f as w,c as d,a as e,t as a,b as i,z as I,w as _,A as C,F as N,x as V,E as l,h as g,o as p,j as k,k as E,n as A,N as B}from"./index-DXyN40AS.js";const b={style:{width:"50%",margin:"20px auto","min-height":"600px"}},z={class:"card",style:{padding:"20px"}},M={style:{"font-size":"24px","font-weight":"bold","text-align":"center"}},L={style:{"margin-top":"15px","text-align":"center",color:"#666"}},S={style:{margin:"0 20px"}},T=["innerHTML"],U={class:"card",style:{"margin-top":"10px",padding:"20px"}},q={style:{"font-size":"18px","font-weight":"bold"}},F={style:{"margin-top":"20px"}},H={style:{"margin-top":"10px","text-align":"right"}},j={style:{"margin-top":"30px",display:"flex","grid-gap":"20px"}},J=["src"],O={style:{display:"flex","align-items":"center","grid-gap":"5px",color:"#666"}},R={style:{"margin-top":"10px","line-height":"25px"}},$={style:{"margin-top":"10px",display:"flex",color:"#666","align-items":"center"}},G=["onClick"],ot={__name:"ArticleDetail",setup(K){const t=D({user:JSON.parse(localStorage.getItem("xm-user")||"{}"),articleId:w.currentRoute.value.query.id,articleData:{},content:null,commentData:[]});(()=>{r.get("/article/selectById/"+t.articleId).then(n=>{n.code==="200"?(t.articleData=n.data,t.articleData.views=t.articleData.views+1,r.put("/article/updateViews",t.articleData)):l.error(n.msg)})})();const x=()=>{if(!t.content){l.warning("请输入评论内容");return}r.post("/comment/add",{content:t.content,articleId:t.articleId}).then(n=>{n.code==="200"&&(l.success("评论成功"),t.content=null,m())})},h=n=>{B.confirm("删除后数据无法恢复，您确定删除吗？","删除确认",{type:"warning"}).then(s=>{r.delete("/comment/delete/"+n).then(c=>{c.code==="200"?(l.success("删除成功"),m()):l.error(c.msg)})}).catch(s=>{console.error(s)})},m=()=>{r.get("/comment/selectAll",{params:{articleId:t.articleId}}).then(n=>{n.code==="200"?t.commentData=n.data:l.error(n.msg)})};return m(),(n,s)=>{const c=I,y=C,v=g("User"),u=E,f=g("Comment");return p(),d("div",b,[e("div",z,[e("div",M,a(t.articleData.title),1),e("div",L,[e("span",null,"发布时间："+a(t.articleData.time),1),e("span",S,"发布人："+a(t.articleData.userName),1),e("span",null,"浏览量："+a(t.articleData.views),1)]),e("div",{innerHTML:t.articleData.content},null,8,T)]),e("div",U,[e("div",q,"评论共 "+a(t.commentData.length)+" 条",1),e("div",F,[i(c,{type:"textarea",rows:5,modelValue:t.content,"onUpdate:modelValue":s[0]||(s[0]=o=>t.content=o),placeholder:"请输入评论"},null,8,["modelValue"])]),e("div",H,[i(y,{type:"primary",style:{padding:"15px 25px"},onClick:x},{default:_(()=>s[1]||(s[1]=[k("评论")])),_:1,__:[1]})]),(p(!0),d(N,null,V(t.commentData,o=>(p(),d("div",j,[e("img",{src:o.userAvatar,alt:"",style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,J),e("div",null,[e("div",O,[e("div",null,a(o.userName),1),i(u,{size:"16"},{default:_(()=>[i(v)]),_:1})]),e("div",R,a(o.content),1),e("div",$,[e("div",null,a(o.time),1),i(u,{size:"16",style:{"margin-left":"20px","margin-right":"5px"}},{default:_(()=>[i(f)]),_:1}),t.user.id===o.userId?(p(),d("div",{key:0,style:{cursor:"pointer"},onClick:Q=>h(o.id)},"删除",8,G)):A("",!0)])])]))),256))])])}}};export{ot as default};
