/* empty css                *//* empty css               *//* empty css                *//* empty css                  *//* empty css                    *//* empty css              *//* empty css                  *//* empty css               *//* empty css                   *//* empty css            *//* empty css               *//* empty css                     *//* empty css                */import{r as c}from"./request-BNOh435t.js";/* empty css                    */import{O as S,r as B,c as w,a as y,n as d,b as l,z as U,w as a,Q as P,A as Y,B as F,C as O,D as q,o as n,R as M,j as i,G as R,t as m,m as p,P as A,K as L,L as $,F as j,x as G,Y as K,E as V}from"./index-DXyN40AS.js";const Q={class:"card",style:{"margin-bottom":"5px"}},H={class:"card",style:{"margin-bottom":"5px"}},J={style:{color:"red"}},W={key:0,class:"card"},X={class:"dialog-footer"},ye={__name:"Orders",setup(Z){const v=S(),e=B({orderNo:null,status:null,total:0,pageNum:1,pageSize:5,orderData:[],form:{},formVisible:!1,roomData:[],rules:{roomId:[{required:!0,message:"请选择房间",trigger:"blur"}],inTime:[{required:!0,message:"请选择登记日期",trigger:"blur"}]}}),x=r=>{e.form={},e.form.orderNo=r.orderNo,e.form.userId=r.userId,e.form.businessId=r.businessId,e.form.typeId=r.typeId,e.form.orderId=r.id,c.get("/room/selectAll",{params:{typeId:r.typeId,status:"空闲"}}).then(t=>{t.code==="200"?(e.roomData=t.data,e.formVisible=!0):V.error(t.msg)})},I=()=>{v.value.validate(r=>{r&&c.post("/registration/add",e.form).then(t=>{t.code==="200"?(V.success("入住登记成功"),e.formVisible=!1,f()):V.error(t.msg)})})},f=()=>{c.get("/businessOrders/selectPage",{params:{pageNum:e.pageNum,pageSize:e.pageSize,orderNo:e.orderNo,status:e.status}}).then(r=>{var t,b;r.code==="200"&&(e.orderData=((t=r.data)==null?void 0:t.list)||[],e.total=(b=r.data)==null?void 0:b.total)})},E=()=>{e.orderNo=null,e.status=null,f()};return f(),(r,t)=>{const b=U,u=M,N=P,_=Y,s=R,g=A,C=F,D=O,k=$,h=K,T=L,z=q;return n(),w("div",null,[y("div",Q,[l(b,{modelValue:e.orderNo,"onUpdate:modelValue":t[0]||(t[0]=o=>e.orderNo=o),"prefix-icon":"Search",style:{width:"240px","margin-right":"10px"},placeholder:"请输入订单编号查询"},null,8,["modelValue"]),l(N,{modelValue:e.status,"onUpdate:modelValue":t[1]||(t[1]=o=>e.status=o),placeholder:"请选择订单状态查询",style:{width:"240px","margin-right":"10px"}},{default:a(()=>[l(u,{label:"待支付",value:"待支付"}),l(u,{label:"待入住",value:"待入住"}),l(u,{label:"已入住",value:"已入住"}),l(u,{label:"已取消",value:"已取消"}),l(u,{label:"已退房",value:"已退房"})]),_:1},8,["modelValue"]),l(_,{type:"info",plain:"",onClick:f},{default:a(()=>t[7]||(t[7]=[i("查询")])),_:1,__:[7]}),l(_,{type:"warning",plain:"",style:{margin:"0 10px"},onClick:E},{default:a(()=>t[8]||(t[8]=[i("重置")])),_:1,__:[8]})]),y("div",H,[l(C,{stripe:"",data:e.orderData},{default:a(()=>[l(s,{prop:"orderNo",label:"订单号","show-overflow-tooltip":""}),l(s,{prop:"userName",label:"下单用户",width:"120"}),l(s,{prop:"businessName",label:"宾馆名称",width:"120"}),l(s,{prop:"typeName",label:"房间类型",width:"120"}),l(s,{prop:"start",label:"入住时间","show-overflow-tooltip":""}),l(s,{prop:"end",label:"离开时间","show-overflow-tooltip":""}),l(s,{prop:"price",label:"总金额"},{default:a(o=>[y("span",J,"￥"+m(o.row.price),1)]),_:1}),l(s,{prop:"payTime",label:"支付时间","show-overflow-tooltip":""}),l(s,{prop:"payNo",label:"支付编号","show-overflow-tooltip":""}),l(s,{prop:"status",label:"订单状态"},{default:a(o=>[o.row.status==="待支付"?(n(),p(g,{key:0,type:"warning"},{default:a(()=>[i(m(o.row.status),1)]),_:2},1024)):d("",!0),o.row.status==="待入住"?(n(),p(g,{key:1,type:"info"},{default:a(()=>[i(m(o.row.status),1)]),_:2},1024)):d("",!0),o.row.status==="已取消"?(n(),p(g,{key:2,type:"danger"},{default:a(()=>[i(m(o.row.status),1)]),_:2},1024)):d("",!0),o.row.status==="已退房"?(n(),p(g,{key:3,type:"primary"},{default:a(()=>[i(m(o.row.status),1)]),_:2},1024)):d("",!0),o.row.status==="已入住"?(n(),p(g,{key:4,type:"success"},{default:a(()=>[i(m(o.row.status),1)]),_:2},1024)):d("",!0)]),_:1}),l(s,{label:"操作",width:"180",fixed:"right"},{default:a(o=>[o.row.status==="待入住"?(n(),p(_,{key:0,type:"info",onClick:ee=>x(o.row)},{default:a(()=>t[9]||(t[9]=[i("入住登记")])),_:2,__:[9]},1032,["onClick"])):d("",!0)]),_:1})]),_:1},8,["data"])]),e.total?(n(),w("div",W,[l(D,{onCurrentChange:f,background:"",layout:"prev, pager, next","page-size":e.pageSize,"current-page":e.pageNum,"onUpdate:currentPage":t[2]||(t[2]=o=>e.pageNum=o),total:e.total},null,8,["page-size","current-page","total"])])):d("",!0),l(z,{title:"入住登记",modelValue:e.formVisible,"onUpdate:modelValue":t[6]||(t[6]=o=>e.formVisible=o),width:"40%","destroy-on-close":""},{footer:a(()=>[y("span",X,[l(_,{onClick:t[5]||(t[5]=o=>e.formVisible=!1)},{default:a(()=>t[10]||(t[10]=[i("取 消")])),_:1,__:[10]}),l(_,{type:"primary",onClick:I},{default:a(()=>t[11]||(t[11]=[i("确 定")])),_:1,__:[11]})])]),default:a(()=>[l(T,{ref_key:"formRef",ref:v,rules:e.rules,model:e.form,"label-width":"90px",style:{padding:"20px"}},{default:a(()=>[l(k,{prop:"roomId",label:"可选房间"},{default:a(()=>[l(N,{modelValue:e.form.roomId,"onUpdate:modelValue":t[3]||(t[3]=o=>e.form.roomId=o),placeholder:"请选择房间类型",style:{width:"100%"}},{default:a(()=>[(n(!0),w(j,null,G(e.roomData,o=>(n(),p(u,{key:o.id,label:o.name+"-"+o.typeName,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(k,{prop:"inTime",label:"入住日期"},{default:a(()=>[l(h,{modelValue:e.form.inTime,"onUpdate:modelValue":t[4]||(t[4]=o=>e.form.inTime=o),type:"date",placeholder:"请选择入住日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1},8,["rules","model"])]),_:1},8,["modelValue"])])}}};export{ye as default};
